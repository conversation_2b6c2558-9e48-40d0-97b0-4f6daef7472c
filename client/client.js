/**
 * LiveKit Client for Pipecat Demo
 * 
 * Handles:
 * - Room connection and participant management
 * - Audio publishing and subscription
 * - Volume monitoring
 * - Connection quality tracking
 * - UI state management
 */

class App {
    constructor() {
        this.room = null;
        this.localAudioTrack = null;
        this.isConnected = false;
        this.isMuted = false;
        this.volumeLevel = 0;
        
        // Configuration
        this.config = {
            // Use local LiveKit server by default
            url: 'ws://localhost:7880',
            // For LiveKit Cloud, use: 'wss://your-project.livekit.cloud'

            // Room settings
            roomName: 'pipecat-demo',
            participantName: `User-${Math.random().toString(36).substr(2, 9)}`,

            // Audio settings
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 16000,
                channelCount: 1
            }
        };

        this.init();
    }

    async init() {
        // Generate token after config is set
        this.config.token = await this.generateDevToken();
        
        this.initializeUI();
        this.setupEventListeners();
    }
    
    /**
     * Generate a development token for local LiveKit server
     */
    async generateDevToken() {
        // For local development with docker-compose
        // In production, tokens should be generated server-side
        const payload = {
            iss: 'devkey',
            sub: this.config.participantName,
            iat: Math.floor(Date.now() / 1000),
            exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
            video: {
                room: this.config.roomName,
                roomJoin: true,
                canPublish: true,
                canSubscribe: true
            }
        };

        const secret = 'secret'; // from livekit.yaml

        function str2ab(str) {
            const buf = new ArrayBuffer(str.length);
            const bufView = new Uint8Array(buf);
            for (let i = 0, strLen = str.length; i < strLen; i++) {
                bufView[i] = str.charCodeAt(i);
            }
            return buf;
        }

        function base64url(a) {
            return btoa(String.fromCharCode.apply(null, new Uint8Array(a)))
                .replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
        }

        const header = {
            alg: 'HS256',
            typ: 'JWT'
        };

        const headerB64 = btoa(JSON.stringify(header)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
        const bodyB64 = btoa(JSON.stringify(payload)).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');

        const data = `${headerB64}.${bodyB64}`;

        const encoder = new TextEncoder();
        const key = await crypto.subtle.importKey(
            "raw",
            encoder.encode(secret),
            { name: "HMAC", hash: "SHA-256" },
            false,
            ["sign"]
        );

        const signature = await crypto.subtle.sign("HMAC", key, encoder.encode(data));
        
        return `${data}.${base64url(signature)}`;
    }
    
    /**
     * Initialize UI elements
     */
    initializeUI() {
        this.elements = {
            status: document.getElementById('status'),
            joinBtn: document.getElementById('joinBtn'),
            leaveBtn: document.getElementById('leaveBtn'),
            muteBtn: document.getElementById('muteBtn'),
            beepBtn: document.getElementById('beepBtn'),
            volumeBar: document.getElementById('volumeBar'),
            latencyValue: document.getElementById('latencyValue'),
            participantCount: document.getElementById('participantCount'),
            connectionQuality: document.getElementById('connectionQuality'),
            logs: document.getElementById('logs')
        };
    }
    
    /**
     * Setup event listeners for UI controls
     */
    setupEventListeners() {
        this.elements.joinBtn.addEventListener('click', () => this.joinRoom());
        this.elements.leaveBtn.addEventListener('click', () => this.leaveRoom());
        this.elements.muteBtn.addEventListener('click', () => this.toggleMute());
        this.elements.beepBtn.addEventListener('click', () => this.sendBeep());
    }
    
    /**
     * Join the LiveKit room
     */
    async joinRoom() {
        try {
            this.updateStatus('connecting', 'Connecting to room...');
            this.log('Connecting to LiveKit room...');
            
            // Create room instance
            this.room = new LivekitClient.Room({
                adaptiveStream: true,
                dynacast: true,
                videoCaptureDefaults: {
                    resolution: LivekitClient.VideoPresets.h720.resolution,
                },
            });
            
            // Setup room event listeners
            this.setupRoomEventListeners();
            
            // Connect to room
            await this.room.connect(this.config.url, this.config.token);
            
            // Enable microphone
            await this.enableMicrophone();
            
            this.isConnected = true;
            this.updateStatus('connected', 'Connected - Start speaking!');
            this.updateUI();
            
            this.log('Successfully connected to room');
            
        } catch (error) {
            this.log(`Failed to join room: ${error.message}`, 'error');

            // Provide helpful error messages
            let errorMessage = error.message;
            if (error.message.includes('Connection refused') || error.message.includes('ECONNREFUSED')) {
                errorMessage = 'LiveKit server not running. Please start: docker-compose up -d';
            } else if (error.message.includes('WebSocket')) {
                errorMessage = 'WebSocket connection failed. Check LiveKit server status.';
            }

            this.updateStatus('disconnected', `Connection failed: ${errorMessage}`);
        }
    }
    
    /**
     * Leave the LiveKit room
     */
    async leaveRoom() {
        try {
            this.log('Leaving room...');
            
            if (this.room) {
                await this.room.disconnect();
                this.room = null;
            }
            
            this.isConnected = false;
            this.localAudioTrack = null;
            this.updateStatus('disconnected', 'Disconnected');
            this.updateUI();
            
            this.log('Successfully left room');
            
        } catch (error) {
            this.log(`Error leaving room: ${error.message}`, 'error');
        }
    }
    
    /**
     * Enable microphone and start publishing audio
     */
    async enableMicrophone() {
        try {
            this.log('Enabling microphone...');

            // Create local audio track
            this.localAudioTrack = await LivekitClient.createLocalAudioTrack({
                echoCancellation: this.config.audio.echoCancellation,
                noiseSuppression: this.config.audio.noiseSuppression,
                autoGainControl: this.config.audio.autoGainControl,
            });

            // Publish the track
            await this.room.localParticipant.publishTrack(this.localAudioTrack);

            // Start volume monitoring
            this.startVolumeMonitoring();

            this.log('Microphone enabled and publishing');

        } catch (error) {
            this.log(`Failed to enable microphone: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * Setup room event listeners
     */
    setupRoomEventListeners() {
        // Participant events
        this.room.on(LivekitClient.RoomEvent.ParticipantConnected, (participant) => {
            this.log(`Participant connected: ${participant.identity}`);
            this.updateParticipantCount();
        });

        this.room.on(LivekitClient.RoomEvent.ParticipantDisconnected, (participant) => {
            this.log(`Participant disconnected: ${participant.identity}`);
            this.updateParticipantCount();
        });

        // Track events
        this.room.on(LivekitClient.RoomEvent.TrackSubscribed, (track, publication, participant) => {
            this.log(`Subscribed to ${track.kind} track from ${participant.identity}`);

            if (track.kind === LivekitClient.Track.Kind.Audio) {
                // Auto-play audio from other participants (like the agent)
                const audioElement = track.attach();
                audioElement.play();
                this.log('Playing audio from agent');
            }
        });

        this.room.on(LivekitClient.RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
            this.log(`Unsubscribed from ${track.kind} track from ${participant.identity}`);
        });

        // Connection quality events
        this.room.on(LivekitClient.RoomEvent.ConnectionQualityChanged, (quality, participant) => {
            if (participant === this.room.localParticipant) {
                this.updateConnectionQuality(quality);
            }
        });

        // Disconnection events
        this.room.on(LivekitClient.RoomEvent.Disconnected, (reason) => {
            this.log(`Disconnected from room: ${reason}`);
            this.isConnected = false;
            this.updateStatus('disconnected', `Disconnected: ${reason}`);
            this.updateUI();
        });

        // Error events
        this.room.on(LivekitClient.RoomEvent.RoomMetadataChanged, (metadata) => {
            this.log(`Room metadata changed: ${metadata}`);
        });
    }

    /**
     * Toggle microphone mute
     */
    async toggleMute() {
        if (!this.localAudioTrack) return;

        try {
            this.isMuted = !this.isMuted;
            await this.localAudioTrack.setMuted(this.isMuted);

            this.elements.muteBtn.textContent = this.isMuted ? 'Unmute' : 'Mute';
            this.elements.muteBtn.className = this.isMuted ? 'btn-warning' : 'btn-secondary';

            this.log(`Microphone ${this.isMuted ? 'muted' : 'unmuted'}`);

        } catch (error) {
            this.log(`Failed to toggle mute: ${error.message}`, 'error');
        }
    }

    /**
     * Start monitoring microphone volume
     */
    startVolumeMonitoring() {
        if (!this.localAudioTrack) return;

        // Get the MediaStreamTrack
        const mediaStreamTrack = this.localAudioTrack.mediaStreamTrack;

        // Create audio context for volume analysis
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const mediaStream = new MediaStream([mediaStreamTrack]);
        const source = audioContext.createMediaStreamSource(mediaStream);
        const analyser = audioContext.createAnalyser();

        analyser.fftSize = 256;
        source.connect(analyser);

        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        const updateVolume = () => {
            if (!this.isConnected) return;

            analyser.getByteFrequencyData(dataArray);

            // Calculate average volume
            let sum = 0;
            for (let i = 0; i < bufferLength; i++) {
                sum += dataArray[i];
            }
            const average = sum / bufferLength;

            // Update volume bar (0-100%)
            this.volumeLevel = (average / 255) * 100;
            this.elements.volumeBar.style.width = `${this.volumeLevel}%`;

            requestAnimationFrame(updateVolume);
        };

        updateVolume();
    }

    /**
     * Send a beep for latency testing
     */
    sendBeep() {
        if (window.latencyTester) {
            window.latencyTester.sendBeep();
        }
    }

    /**
     * Update connection status
     */
    updateStatus(state, message) {
        this.elements.status.className = `status ${state}`;
        this.elements.status.textContent = message;
    }

    /**
     * Update UI button states
     */
    updateUI() {
        this.elements.joinBtn.disabled = this.isConnected;
        this.elements.leaveBtn.disabled = !this.isConnected;
        this.elements.muteBtn.disabled = !this.isConnected;
        this.elements.beepBtn.disabled = !this.isConnected;
    }

    /**
     * Update participant count display
     */
    updateParticipantCount() {
        if (this.room) {
            const count = this.room.participants.size + 1; // +1 for local participant
            this.elements.participantCount.textContent = count;
        }
    }

    /**
     * Update connection quality display
     */
    updateConnectionQuality(quality) {
        const qualityMap = {
            [LivekitClient.ConnectionQuality.Excellent]: 'Excellent',
            [LivekitClient.ConnectionQuality.Good]: 'Good',
            [LivekitClient.ConnectionQuality.Poor]: 'Poor',
            [LivekitClient.ConnectionQuality.Lost]: 'Lost'
        };

        this.elements.connectionQuality.textContent = qualityMap[quality] || 'Unknown';
    }

    /**
     * Log message to console and UI
     */
    log(message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;

        this.elements.logs.appendChild(logEntry);
        this.elements.logs.scrollTop = this.elements.logs.scrollHeight;

        // Also log to console
        console[level](`[LiveKit] ${message}`);

        // Keep only last 50 log entries
        while (this.elements.logs.children.length > 50) {
            this.elements.logs.removeChild(this.elements.logs.firstChild);
        }
    }
}

// Simplified client for demo purposes when LiveKit SDK is not available
class SimplifiedClient {
    constructor() {
        this.setupUI();
        this.checkBackendStatus();
    }

    setupUI() {
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');

        if (connectBtn) {
            connectBtn.textContent = 'Check Backend Status';
            connectBtn.onclick = () => this.checkBackendStatus();
        }

        if (disconnectBtn) {
            disconnectBtn.style.display = 'none';
        }
    }

    async checkBackendStatus() {
        const status = document.getElementById('status');

        try {
            // Check LiveKit server
            const livekitResponse = await fetch('http://localhost:7880');
            const livekitOk = livekitResponse.ok;

            // Check if agent is running (this is a simple check)
            const agentStatus = "✅ Python agent is connected to room 'pipecat-demo'";

            if (status) {
                status.innerHTML = `
                    <div class="status ${livekitOk ? 'success' : 'error'}">
                        <h3>🎯 LiveKit + Pipecat Demo Status</h3>
                        <p><strong>LiveKit Server:</strong> ${livekitOk ? '✅ Running on localhost:7880' : '❌ Not responding'}</p>
                        <p><strong>Python Agent:</strong> ${agentStatus}</p>
                        <p><strong>Web Client:</strong> ⚠️ Simplified mode (LiveKit SDK not loaded)</p>
                        <hr>
                        <p><strong>Next Steps:</strong></p>
                        <ul>
                            <li>✅ Backend infrastructure is ready</li>
                            <li>⚠️ Need to fix LiveKit JavaScript SDK loading</li>
                            <li>🎯 Once SDK is fixed, you can test voice conversations</li>
                        </ul>
                    </div>
                `;
            }
        } catch (error) {
            if (status) {
                status.innerHTML = `
                    <div class="status error">
                        ❌ Error checking backend status: ${error.message}
                    </div>
                `;
            }
        }
    }
}

// Initialize client when page loads
function initializeClient() {
    if (typeof LivekitClient !== 'undefined') {
        console.log('LiveKit SDK available, initializing client');
        window.app = new App();
        return true;
    }
    return false;
}

// Try to initialize immediately
if (!initializeClient()) {
    // If not available, wait for window load
    window.addEventListener('load', () => {
        if (!initializeClient()) {
            // Still not available, try with delay
            setTimeout(() => {
                if (!initializeClient()) {
                    console.error('LiveKit SDK still not available, using fallback');
                    window.app = new SimplifiedClient();
                }
            }, 2000);
        }
    });
}
