<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit + Pipecat Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: white;
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        .main-content {
            flex: 3;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .log-panel {
            flex: 1;
            background: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-left: 1px solid #ddd;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
            font-weight: 400;
        }

        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .status {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
            border: 1px solid #ddd;
            background: #f9f9f9;
        }

        .status.ready {
            background: #f0f8f0;
            color: #2e7d32;
            border-color: #4caf50;
        }

        .status.connecting {
            background: #fff8e1;
            color: #ef6c00;
            border-color: #ff9800;
        }

        .status.connected {
            background: #e3f2fd;
            color: #1565c0;
            border-color: #2196f3;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
            border-color: #f44336;
        }

        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }

        button {
            padding: 10px 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            background: white;
            color: #333;
        }

        button:hover:not(:disabled) {
            background: #f5f5f5;
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .primary-btn {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .primary-btn:hover:not(:disabled) {
            background: #0056b3;
        }

        .secondary-btn {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .secondary-btn:hover:not(:disabled) {
            background: #545b62;
        }

        .test-btn {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .test-btn:hover:not(:disabled) {
            background: #1e7e34;
        }

        .instructions {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 20px;
            margin-top: 20px;
            max-width: 400px;
        }

        .instructions h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .instructions ol {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 5px;
            color: #666;
            font-size: 14px;
        }

        .log-header {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }

        .log-entry.info {
            color: #28a745;
        }

        .log-entry.warning {
            color: #ffc107;
        }

        .log-entry.error {
            color: #dc3545;
        }

        .log-entry.debug {
            color: #6c757d;
        }

        .timestamp {
            color: #999;
            font-size: 11px;
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }

            .main-content {
                flex: 2;
                padding: 20px;
            }

            .log-panel {
                flex: 1;
                border-left: none;
                border-top: 1px solid #ddd;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            button {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="main-content">
            <h1>LiveKit + Pipecat Demo</h1>
            <p class="subtitle">Real-time voice AI with echo response</p>
            
            <div id="status" class="status ready">
                [Ready] Waiting to connect...
            </div>
            
            <div class="controls">
                <button id="joinBtn" class="primary-btn">Join Room</button>
                <button id="leaveBtn" class="secondary-btn" disabled>Leave Room</button>
                <button id="testLatencyBtn" class="test-btn" disabled>Test Latency</button>
            </div>
            
            <div class="instructions">
                <h3>How to Test</h3>
                <ol>
                    <li>Click "Join Room" to connect</li>
                    <li>Allow microphone access when prompted</li>
                    <li>Speak into your microphone</li>
                    <li>Listen for the agent's echo response with "...got it"</li>
                    <li>Check the logs for detailed processing information</li>
                </ol>
            </div>
        </div>
        
        <div class="log-panel">
            <div class="log-header">System Logs</div>
            <div id="logs"></div>
        </div>
    </div>

    <!-- LiveKit SDK -->
    <script src="https://unpkg.com/livekit-client@2.6.0/dist/livekit-client.umd.js"></script>
    
    <!-- Our client code -->
    <script src="client.js?v=4"></script>
    <script src="latency-test.js?v=4"></script>
</body>
</html>
