<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveKit + Pipecat Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        .main-content {
            flex: 3;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: white;
        }

        .log-panel {
            flex: 1;
            background: #1e1e1e;
            color: #e0e0e0;
            padding: 20px;
            overflow-y: auto;
            border-left: 1px solid #333;
        }

        h1 {
            font-size: 2.5rem;
            font-weight: 300;
            color: #2c3e50;
            margin-bottom: 10px;
            text-align: center;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin-bottom: 40px;
            text-align: center;
        }

        .status {
            padding: 16px 24px;
            border-radius: 12px;
            margin-bottom: 30px;
            font-weight: 500;
            font-size: 1.1rem;
            text-align: center;
            min-width: 300px;
        }

        .status.ready {
            background: #e8f5e8;
            color: #2e7d32;
            border: 2px solid #4caf50;
        }

        .status.connecting {
            background: #fff3e0;
            color: #ef6c00;
            border: 2px solid #ff9800;
        }

        .status.connected {
            background: #e3f2fd;
            color: #1565c0;
            border: 2px solid #2196f3;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 2px solid #f44336;
        }

        .controls {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        button {
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 140px;
        }

        .primary-btn {
            background: #4CAF50;
            color: white;
        }

        .primary-btn:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-1px);
        }

        .secondary-btn {
            background: #f44336;
            color: white;
        }

        .secondary-btn:hover:not(:disabled) {
            background: #d32f2f;
            transform: translateY(-1px);
        }

        .test-btn {
            background: #2196F3;
            color: white;
        }

        .test-btn:hover:not(:disabled) {
            background: #1976D2;
            transform: translateY(-1px);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .instructions {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #2196F3;
            max-width: 500px;
        }

        .instructions h3 {
            color: #1976D2;
            margin-bottom: 10px;
        }

        .instructions ol {
            margin-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
            color: #555;
        }

        .log-header {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #fff;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 4px 0;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .log-entry.info {
            color: #4CAF50;
        }

        .log-entry.warning {
            color: #FF9800;
        }

        .log-entry.error {
            color: #f44336;
        }

        .log-entry.debug {
            color: #9E9E9E;
        }

        .timestamp {
            color: #666;
            font-size: 11px;
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }

            .main-content {
                flex: 2;
                padding: 20px;
            }

            .log-panel {
                flex: 1;
                border-left: none;
                border-top: 1px solid #333;
            }

            h1 {
                font-size: 2rem;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            button {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="main-content">
            <h1>LiveKit + Pipecat Demo</h1>
            <p class="subtitle">Real-time voice AI with echo response</p>

            <div id="status" class="status ready">
                [Ready] Waiting to connect...
            </div>

            <div class="controls">
                <button id="joinBtn" class="primary-btn">Join Room</button>
                <button id="leaveBtn" class="secondary-btn" disabled>Leave Room</button>
                <button id="testLatencyBtn" class="test-btn" disabled>Test Latency</button>
            </div>

            <div class="instructions">
                <h3>How to Test</h3>
                <ol>
                    <li>Click "Join Room" to connect</li>
                    <li>Allow microphone access when prompted</li>
                    <li>Speak into your microphone</li>
                    <li>Listen for the agent's echo response with "...got it"</li>
                    <li>Check the logs for detailed processing information</li>
                </ol>
            </div>
        </div>

        <div class="log-panel">
            <div class="log-header">🔍 System Logs</div>
            <div id="logs"></div>
        </div>
    </div>

    <!-- Our client code -->
    <script src="client.js"></script>
    <script src="latency-test.js"></script>
</body>
</html>
