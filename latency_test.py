#!/usr/bin/env python3

"""
Latency Test Script for LiveKit + Pipecat Demo

This script measures mouth-to-ear latency by:
1. Playing a beep sound through the microphone
2. Measuring time until the agent's response is heard
3. Calculating round-trip latency

Usage:
    python latency_test.py
"""

import asyncio
import time
import numpy as np
import sounddevice as sd
import logging
from typing import List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LatencyTester:
    def __init__(self):
        self.sample_rate = 16000
        self.beep_duration = 0.5  # seconds
        self.beep_frequency = 1000  # Hz
        self.latencies: List[float] = []
        
    def generate_beep(self) -> np.ndarray:
        """Generate a beep sound"""
        t = np.linspace(0, self.beep_duration, int(self.sample_rate * self.beep_duration))
        beep = 0.3 * np.sin(2 * np.pi * self.beep_frequency * t)
        return beep.astype(np.float32)
    
    def play_beep_and_measure(self) -> float:
        """Play a beep and measure latency manually"""
        beep = self.generate_beep()
        
        logger.info("🔊 Playing beep sound...")
        start_time = time.time()
        
        # Play the beep
        sd.play(beep, self.sample_rate)
        sd.wait()  # Wait until the beep finishes playing
        
        # Manual measurement - user presses Enter when they hear the response
        input("Press Enter when you hear the agent's response...")
        end_time = time.time()
        
        latency = (end_time - start_time) * 1000  # Convert to milliseconds
        logger.info(f"⏱️  Measured latency: {latency:.1f}ms")
        
        return latency
    
    def run_test(self, num_tests: int = 3):
        """Run multiple latency tests"""
        logger.info(f"🧪 Starting latency test with {num_tests} measurements")
        logger.info("📋 Instructions:")
        logger.info("   1. Make sure you're connected to the LiveKit room")
        logger.info("   2. When you hear a beep, wait for the agent's response")
        logger.info("   3. Press Enter as soon as you hear the agent say the beep back")
        logger.info("")
        
        for i in range(num_tests):
            logger.info(f"🔄 Test {i+1}/{num_tests}")
            
            # Wait a bit between tests
            if i > 0:
                time.sleep(2)
            
            try:
                latency = self.play_beep_and_measure()
                self.latencies.append(latency)
            except KeyboardInterrupt:
                logger.info("❌ Test interrupted by user")
                break
            except Exception as e:
                logger.error(f"❌ Error during test {i+1}: {e}")
        
        # Calculate statistics
        if self.latencies:
            avg_latency = np.mean(self.latencies)
            min_latency = np.min(self.latencies)
            max_latency = np.max(self.latencies)
            
            logger.info("")
            logger.info("📊 LATENCY TEST RESULTS:")
            logger.info(f"   Tests completed: {len(self.latencies)}")
            logger.info(f"   Average latency: {avg_latency:.1f}ms")
            logger.info(f"   Minimum latency: {min_latency:.1f}ms")
            logger.info(f"   Maximum latency: {max_latency:.1f}ms")
            logger.info(f"   All measurements: {[f'{l:.1f}ms' for l in self.latencies]}")
            
            # Check if we meet the <600ms requirement
            if avg_latency < 600:
                logger.info("✅ SUCCESS: Average latency is under 600ms!")
            else:
                logger.info("⚠️  WARNING: Average latency exceeds 600ms")
        else:
            logger.info("❌ No successful measurements recorded")

def main():
    """Main function"""
    try:
        # Check if sounddevice is available
        import sounddevice as sd
        logger.info("🎵 Audio system initialized")
        
        tester = LatencyTester()
        tester.run_test(num_tests=3)
        
    except ImportError:
        logger.error("❌ sounddevice not installed. Install with: pip install sounddevice")
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
