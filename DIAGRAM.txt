Mic (User's Browser)
 │
 │ (WebRTC: Audio Stream)
 ▼
LiveKit SFU (Media Server)
 │ │
 │ └──────────────────────────────┐
 │ (Audio forwarded to Agent)     │ (Audio forwarded to other users, if any)
 ▼                                │
Pipecat Agent (Python Process)      │
 │                                │
 │ 1. Receives Audio Stream       │
 │ 2. VAD (detects speech)        │
 │ 3. STT (transcribes to text)   │
 │ 4. Logic ("...got it!")        │
 │ 5. TTS (synthesizes speech)    │
 │                                │
 │ (WebRTC: Publishes new Audio Stream)
 ▼                                │
LiveKit SFU (Media Server)        │
 │                                │
 │ ──────────────────────────────┘
 │ (Forwards Agent's audio to User)
 ▼
Speaker (User's Browser)