#!/usr/bin/env python3

"""
System Test Script for LiveKit + Pipecat Demo

This script verifies that all components are running and working together.
"""

import subprocess
import time
import requests
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_docker_containers():
    """Check if LiveKit and Redis containers are running"""
    try:
        result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
        if result.returncode == 0:
            output = result.stdout
            livekit_running = 'livekit-server' in output
            redis_running = 'redis' in output
            
            logger.info(f"🐳 LiveKit container: {'✅ Running' if livekit_running else '❌ Not running'}")
            logger.info(f"🐳 Redis container: {'✅ Running' if redis_running else '❌ Not running'}")
            
            return livekit_running and redis_running
        else:
            logger.error("❌ Failed to check Docker containers")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking Docker: {e}")
        return False

def check_web_client():
    """Check if web client is accessible"""
    try:
        response = requests.get('http://localhost:8080', timeout=5)
        if response.status_code == 200:
            logger.info("🌐 Web client: ✅ Running on http://localhost:8080")
            return True
        else:
            logger.error(f"❌ Web client returned status {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Web client not accessible: {e}")
        return False

def check_livekit_server():
    """Check if LiveKit server is accessible"""
    try:
        # Try to connect to LiveKit WebSocket endpoint
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 7880))
        sock.close()
        
        if result == 0:
            logger.info("🎥 LiveKit server: ✅ Running on localhost:7880")
            return True
        else:
            logger.error("❌ LiveKit server not accessible on localhost:7880")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking LiveKit server: {e}")
        return False

def check_agent_process():
    """Check if Pipecat agent is running"""
    try:
        result = subprocess.run(['pgrep', '-f', 'spawn_agent.py'], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            logger.info("🤖 Pipecat agent: ✅ Running")
            return True
        else:
            logger.error("❌ Pipecat agent not running")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking agent process: {e}")
        return False

def print_system_status():
    """Print overall system status"""
    logger.info("")
    logger.info("🔍 SYSTEM STATUS CHECK")
    logger.info("=" * 50)
    
    docker_ok = check_docker_containers()
    livekit_ok = check_livekit_server()
    web_ok = check_web_client()
    agent_ok = check_agent_process()
    
    logger.info("")
    logger.info("📋 SUMMARY:")
    
    all_ok = docker_ok and livekit_ok and web_ok and agent_ok
    
    if all_ok:
        logger.info("🎉 ALL SYSTEMS OPERATIONAL!")
        logger.info("")
        logger.info("🚀 Ready to test:")
        logger.info("   1. Open http://localhost:8080 in your browser")
        logger.info("   2. Click 'Join Room'")
        logger.info("   3. Speak into your microphone")
        logger.info("   4. Listen for the agent's echo response")
        logger.info("")
        logger.info("📊 To measure latency:")
        logger.info("   - Watch the agent logs for latency measurements")
        logger.info("   - Or run: python latency_test.py")
    else:
        logger.error("❌ SYSTEM NOT READY - Some components are not running")
        
        if not docker_ok:
            logger.error("   → Start Docker containers: docker-compose up -d")
        if not web_ok:
            logger.error("   → Start web client: cd client && python -m http.server 8080")
        if not agent_ok:
            logger.error("   → Start agent: cd agent && source venv/bin/activate && python spawn_agent.py")

def main():
    """Main function"""
    print_system_status()

if __name__ == "__main__":
    main()
