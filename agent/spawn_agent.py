#!/usr/bin/env python3

"""
LiveKit + Pipecat Integration Demo Agent

This agent:
1. Connects to a LiveKit room as a participant
2. Listens for user audio input
3. Converts speech to text (STT)
4. Adds "...got it" suffix to the text
5. Converts back to speech (TTS)
6. Publishes audio response to the room

Features:
- Real-time audio processing
- Barge-in support (user can interrupt)
- Latency optimization
- Error handling and reconnection
"""

import asyncio
import logging
import signal
import sys
import os
import time
import jwt
from typing import Optional

# Add current directory to path for config import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    import config
except ImportError:
    print("❌ config.py not found. Please copy config.py.template to config.py and configure your credentials.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Check if we have the required packages
try:
    from pipecat.frames.frames import Frame, AudioRawFrame, TextFrame
    from pipecat.pipeline.pipeline import Pipeline
    from pipecat.pipeline.runner import PipelineRunner
    from pipecat.pipeline.task import PipelineTask
    from pipecat.services.openai import OpenAITTSService, OpenAISTTService
    from pipecat.transports.services.livekit import LiveKitTransport, LiveKitParams
    from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
    from pipecat.processors.frame_processor import FrameDirection, FrameProcessor
    from pipecat.audio.vad.silero import SileroVADAnalyzer
    from pipecat.audio.vad.vad_analyzer import VADParams

    logger.info("✅ Pipecat imports successful")

except ImportError as e:
    logger.error(f"❌ Failed to import Pipecat: {e}")
    logger.error("Please install Pipecat: pip install pipecat-ai")
    sys.exit(1)


def generate_access_token():
    """Generate a LiveKit access token for the agent"""
    from livekit import api

    # Create access token
    token = api.AccessToken(config.LIVEKIT_API_KEY, config.LIVEKIT_API_SECRET) \
        .with_identity(config.AGENT_NAME) \
        .with_name(config.AGENT_NAME) \
        .with_grants(api.VideoGrants(
            room_join=True,
            room=config.ROOM_NAME,
            can_publish=True,
            can_subscribe=True
        ))

    return token.to_jwt()


class EchoProcessor(FrameProcessor):
    """Simple processor that adds 'got it' to user input"""

    def __init__(self):
        super().__init__()
        self.response_suffix = config.ECHO_SUFFIX
        self.speech_start_time = None
        self._started = False

    async def process_frame(self, frame: Frame, direction: FrameDirection):
        """Process incoming frames"""
        # Log all frames for debugging
        frame_type = frame.__class__.__name__
        logger.debug(f"🔄 EchoProcessor received: {frame_type}")

        # Call parent's process_frame first to handle framework initialization
        await super().process_frame(frame, direction)

        # Handle StartFrame to mark processor as started
        if frame_type == 'StartFrame':
            self._started = True
            logger.info("🚀 EchoProcessor started and ready")
            return

        # Track when user starts speaking for latency measurement
        if 'UserStartedSpeaking' in frame_type:
            self.speech_start_time = time.time()
            logger.info(f"🎤 User started speaking at {self.speech_start_time:.3f}")

        # Process text input from STT
        if isinstance(frame, TextFrame):
            # Only process text frames if we're started
            if not self._started:
                logger.warning("⚠️  EchoProcessor not started yet, skipping TextFrame")
                await self.push_frame(frame, direction)
                return

            user_text = frame.text.strip()
            logger.info(f"📝 Received TextFrame: '{user_text}' (length: {len(user_text)})")

            if user_text:
                # Calculate latency if we have a start time
                if self.speech_start_time:
                    latency_ms = (time.time() - self.speech_start_time) * 1000
                    logger.info(f"⏱️  STT Latency: {latency_ms:.1f}ms")

                # Create response with suffix
                response_text = f"{user_text}{self.response_suffix}"
                logger.info(f"💬 User: '{user_text}' -> Agent: '{response_text}'")

                # Create new text frame with response
                response_frame = TextFrame(response_text)
                await self.push_frame(response_frame, direction)

                # Reset for next measurement
                self.speech_start_time = None
                return
            else:
                # Handle empty input
                logger.info("🔇 Empty text received, sending fallback response")
                response_frame = TextFrame("I didn't catch that. Could you repeat?")
                await self.push_frame(response_frame, direction)
                return

        # Always pass through all other frames
        await self.push_frame(frame, direction)


async def main():
    """Main function to start the agent"""
    logger.info("🤖 Starting LiveKit + Pipecat Demo Agent")

    # Validate configuration
    if not config.OPENAI_API_KEY or config.OPENAI_API_KEY == "your-openai-api-key":
        logger.error("❌ Please set your OpenAI API key in config.py")
        return

    if not config.LIVEKIT_URL or not config.LIVEKIT_API_KEY or not config.LIVEKIT_API_SECRET:
        logger.error("❌ Please set your LiveKit credentials in config.py")
        return

    try:
        # Generate access token
        token = generate_access_token()

        # Initialize transport with VAD
        transport = LiveKitTransport(
            url=config.LIVEKIT_URL,
            token=token,
            room_name=config.ROOM_NAME,
            params=LiveKitParams(
                participant_name=config.AGENT_NAME,
                audio_in_enabled=True,
                audio_out_enabled=True,
                vad_analyzer=SileroVADAnalyzer(
                    params=VADParams(
                        stop_secs=0.5,  # More sensitive - detect end of speech faster
                        start_secs=0.2,  # Detect start of speech faster
                        min_volume=0.6   # Lower threshold for voice detection
                    )
                ),
            )
        )

        # Initialize STT service
        stt = OpenAISTTService(
            api_key=config.OPENAI_API_KEY,
            model="whisper-1",
        )

        # Initialize TTS service
        tts = OpenAITTSService(
            api_key=config.OPENAI_API_KEY,
            voice="alloy",
        )

        # Initialize our echo processor
        echo_processor = EchoProcessor()

        # Create pipeline
        pipeline = Pipeline([
            transport.input(),   # Audio input from LiveKit
            stt,                # Speech to text
            echo_processor,     # Our echo logic
            tts,                # Text to speech
            transport.output(), # Audio output to LiveKit
        ])

        # Create and run the task
        task = PipelineTask(pipeline)

        logger.info(f"🚀 Agent connecting to room: {config.ROOM_NAME}")
        logger.info("💬 Ready to receive audio and respond with echo + 'got it'")

        # Run the pipeline
        runner = PipelineRunner()
        await runner.run(task)

    except KeyboardInterrupt:
        logger.info("👋 Agent stopped by user")
    except Exception as e:
        logger.error(f"❌ Agent failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    # Handle graceful shutdown
    def signal_handler(signum, frame):
        logger.info("Received shutdown signal, cleaning up...")
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Run the async main function
    asyncio.run(main())
