# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from ..core.client_wrapper import SyncClientWrapper
from .types.gender_presentation import GenderPresentation
from .types.voice_expand_options import VoiceExpandOptions
from ..core.request_options import RequestOptions
from ..core.pagination import SyncPager
from .types.voice import Voice
from .types.get_voices_response import GetVoicesResponse
from ..core.pydantic_utilities import parse_obj_as
from json.decoder import J<PERSON><PERSON><PERSON>ode<PERSON>rror
from ..core.api_error import ApiError
from .. import core
from ..tts.types.supported_language import SupportedLanguage
from .types.clone_mode import <PERSON>loneMode
from .types.voice_id import VoiceId
from .types.voice_metadata import VoiceMetadata
from ..core.jsonable_encoder import jsonable_encoder
from .types.localize_target_language import LocalizeTargetLanguage
from .types.gender import Gender
from .requests.localize_dialect import LocalizeDialectParams
from ..core.serialization import convert_and_respect_annotation_metadata
from .requests.mix_voice_specifier import MixVoiceSpecifierParams
from .types.embedding_response import EmbeddingResponse
from ..embedding.types.embedding import Embedding
from .types.base_voice_id import BaseVoiceId
from ..core.client_wrapper import AsyncClientWrapper
from ..core.pagination import AsyncPager

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class VoicesClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def list(
        self,
        *,
        limit: typing.Optional[int] = None,
        starting_after: typing.Optional[str] = None,
        ending_before: typing.Optional[str] = None,
        is_owner: typing.Optional[bool] = None,
        is_starred: typing.Optional[bool] = None,
        gender: typing.Optional[GenderPresentation] = None,
        expand: typing.Optional[typing.Sequence[VoiceExpandOptions]] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> SyncPager[Voice]:
        """
        Parameters
        ----------
        limit : typing.Optional[int]
            The number of Voices to return per page, ranging between 1 and 100.

        starting_after : typing.Optional[str]
            A cursor to use in pagination. `starting_after` is a Voice ID that defines your
            place in the list. For example, if you make a /voices request and receive 100
            objects, ending with `voice_abc123`, your subsequent call can include
            `starting_after=voice_abc123` to fetch the next page of the list.

        ending_before : typing.Optional[str]
            A cursor to use in pagination. `ending_before` is a Voice ID that defines your
            place in the list. For example, if you make a /voices request and receive 100
            objects, starting with `voice_abc123`, your subsequent call can include
            `ending_before=voice_abc123` to fetch the previous page of the list.

        is_owner : typing.Optional[bool]
            Whether to only return voices owned by the current user.

        is_starred : typing.Optional[bool]
            Whether to only return starred voices.

        gender : typing.Optional[GenderPresentation]
            The gender presentation of the voices to return.

        expand : typing.Optional[typing.Sequence[VoiceExpandOptions]]
            Additional fields to include in the response.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        SyncPager[Voice]

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        response = client.voices.list()
        for item in response:
            yield item
        # alternatively, you can paginate page-by-page
        for page in response.iter_pages():
            yield page
        """
        _response = self._client_wrapper.httpx_client.request(
            "voices/",
            method="GET",
            params={
                "limit": limit,
                "starting_after": starting_after,
                "ending_before": ending_before,
                "is_owner": is_owner,
                "is_starred": is_starred,
                "gender": gender,
                "expand[]": expand,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _parsed_response = typing.cast(
                    GetVoicesResponse,
                    parse_obj_as(
                        type_=GetVoicesResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                _parsed_next = _parsed_response.next_page
                _has_next = _parsed_next is not None and _parsed_next != ""
                _get_next = lambda: self.list(
                    limit=limit,
                    starting_after=_parsed_next,
                    ending_before=ending_before,
                    is_owner=is_owner,
                    is_starred=is_starred,
                    gender=gender,
                    expand=expand,
                    request_options=request_options,
                )
                _items = _parsed_response.data
                return SyncPager(has_next=_has_next, items=_items, get_next=_get_next)
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def clone(
        self,
        *,
        clip: core.File,
        name: str,
        language: SupportedLanguage,
        mode: CloneMode,
        description: typing.Optional[str] = OMIT,
        enhance: typing.Optional[bool] = OMIT,
        base_voice_id: typing.Optional[VoiceId] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> VoiceMetadata:
        """
        Clone a voice from an audio clip. This endpoint has two modes, stability and similarity.

        Similarity mode clones are more similar to the source clip, but may reproduce background noise. For these, use an audio clip about 5 seconds long.

        Stability mode clones are more stable, but may not sound as similar to the source clip. For these, use an audio clip 10-20 seconds long.

        Parameters
        ----------
        clip : core.File
            See core.File for more documentation

        name : str
            The name of the voice.

        language : SupportedLanguage
            The language of the voice.

        mode : CloneMode
            Tradeoff between similarity and stability. Similarity clones sound more like the source clip, but may reproduce background noise. Stability clones always sound like a studio recording, but may not sound as similar to the source clip.

        description : typing.Optional[str]
            A description for the voice.

        enhance : typing.Optional[bool]
            Whether to apply AI enhancements to the clip to reduce background noise. This leads to cleaner generated speech at the cost of reduced similarity to the source clip.

        base_voice_id : typing.Optional[VoiceId]
            Optional base voice ID that the cloned voice is derived from.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceMetadata

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voices.clone(
            name="A high-stability cloned voice",
            description="Copied from Cartesia docs",
            mode="stability",
            language="en",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "voices/clone",
            method="POST",
            data={
                "name": name,
                "description": description,
                "language": language,
                "mode": mode,
                "enhance": enhance,
                "base_voice_id": base_voice_id,
            },
            files={
                "clip": clip,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    VoiceMetadata,
                    parse_obj_as(
                        type_=VoiceMetadata,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete(self, id: VoiceId, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Parameters
        ----------
        id : VoiceId

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voices.delete(
            id="id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"voices/{jsonable_encoder(id)}",
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update(
        self, id: VoiceId, *, name: str, description: str, request_options: typing.Optional[RequestOptions] = None
    ) -> Voice:
        """
        Parameters
        ----------
        id : VoiceId

        name : str
            The name of the voice.

        description : str
            The description of the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voices.update(
            id="id",
            name="name",
            description="description",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"voices/{jsonable_encoder(id)}",
            method="PATCH",
            json={
                "name": name,
                "description": description,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Voice,
                    parse_obj_as(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get(self, id: VoiceId, *, request_options: typing.Optional[RequestOptions] = None) -> Voice:
        """
        Parameters
        ----------
        id : VoiceId

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voices.get(
            id="id",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            f"voices/{jsonable_encoder(id)}",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Voice,
                    parse_obj_as(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def localize(
        self,
        *,
        voice_id: str,
        name: str,
        description: str,
        language: LocalizeTargetLanguage,
        original_speaker_gender: Gender,
        dialect: typing.Optional[LocalizeDialectParams] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> VoiceMetadata:
        """
        Create a new voice from an existing voice localized to a new language and dialect.

        Parameters
        ----------
        voice_id : str
            The ID of the voice to localize.

        name : str
            The name of the new localized voice.

        description : str
            The description of the new localized voice.

        language : LocalizeTargetLanguage

        original_speaker_gender : Gender

        dialect : typing.Optional[LocalizeDialectParams]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceMetadata

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voices.localize(
            voice_id="694f9389-aac1-45b6-b726-9d9369183238",
            name="Sarah Peninsular Spanish",
            description="Sarah Voice in Peninsular Spanish",
            language="es",
            original_speaker_gender="female",
            dialect="pe",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "voices/localize",
            method="POST",
            json={
                "voice_id": voice_id,
                "name": name,
                "description": description,
                "language": language,
                "original_speaker_gender": original_speaker_gender,
                "dialect": convert_and_respect_annotation_metadata(
                    object_=dialect, annotation=LocalizeDialectParams, direction="write"
                ),
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    VoiceMetadata,
                    parse_obj_as(
                        type_=VoiceMetadata,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def mix(
        self,
        *,
        voices: typing.Sequence[MixVoiceSpecifierParams],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EmbeddingResponse:
        """
        Parameters
        ----------
        voices : typing.Sequence[MixVoiceSpecifierParams]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EmbeddingResponse

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voices.mix(
            voices=[{"id": "id", "weight": 1.1}, {"id": "id", "weight": 1.1}],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "voices/mix",
            method="POST",
            json={
                "voices": convert_and_respect_annotation_metadata(
                    object_=voices, annotation=typing.Sequence[MixVoiceSpecifierParams], direction="write"
                ),
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EmbeddingResponse,
                    parse_obj_as(
                        type_=EmbeddingResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create(
        self,
        *,
        name: str,
        description: str,
        embedding: Embedding,
        language: typing.Optional[SupportedLanguage] = OMIT,
        base_voice_id: typing.Optional[BaseVoiceId] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Voice:
        """
        Create voice from raw features. If you'd like to clone a voice from an audio file, please use Clone Voice instead.

        Parameters
        ----------
        name : str
            The name of the voice.

        description : str
            The description of the voice.

        embedding : Embedding

        language : typing.Optional[SupportedLanguage]

        base_voice_id : typing.Optional[BaseVoiceId]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voices.create(
            name="name",
            description="description",
            embedding=[1.1, 1.1],
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "voices/",
            method="POST",
            json={
                "name": name,
                "description": description,
                "embedding": embedding,
                "language": language,
                "base_voice_id": base_voice_id,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Voice,
                    parse_obj_as(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncVoicesClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def list(
        self,
        *,
        limit: typing.Optional[int] = None,
        starting_after: typing.Optional[str] = None,
        ending_before: typing.Optional[str] = None,
        is_owner: typing.Optional[bool] = None,
        is_starred: typing.Optional[bool] = None,
        gender: typing.Optional[GenderPresentation] = None,
        expand: typing.Optional[typing.Sequence[VoiceExpandOptions]] = None,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> AsyncPager[Voice]:
        """
        Parameters
        ----------
        limit : typing.Optional[int]
            The number of Voices to return per page, ranging between 1 and 100.

        starting_after : typing.Optional[str]
            A cursor to use in pagination. `starting_after` is a Voice ID that defines your
            place in the list. For example, if you make a /voices request and receive 100
            objects, ending with `voice_abc123`, your subsequent call can include
            `starting_after=voice_abc123` to fetch the next page of the list.

        ending_before : typing.Optional[str]
            A cursor to use in pagination. `ending_before` is a Voice ID that defines your
            place in the list. For example, if you make a /voices request and receive 100
            objects, starting with `voice_abc123`, your subsequent call can include
            `ending_before=voice_abc123` to fetch the previous page of the list.

        is_owner : typing.Optional[bool]
            Whether to only return voices owned by the current user.

        is_starred : typing.Optional[bool]
            Whether to only return starred voices.

        gender : typing.Optional[GenderPresentation]
            The gender presentation of the voices to return.

        expand : typing.Optional[typing.Sequence[VoiceExpandOptions]]
            Additional fields to include in the response.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        AsyncPager[Voice]

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            response = await client.voices.list()
            async for item in response:
                yield item
            # alternatively, you can paginate page-by-page
            async for page in response.iter_pages():
                yield page


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "voices/",
            method="GET",
            params={
                "limit": limit,
                "starting_after": starting_after,
                "ending_before": ending_before,
                "is_owner": is_owner,
                "is_starred": is_starred,
                "gender": gender,
                "expand[]": expand,
            },
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                _parsed_response = typing.cast(
                    GetVoicesResponse,
                    parse_obj_as(
                        type_=GetVoicesResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
                _parsed_next = _parsed_response.next_page
                _has_next = _parsed_next is not None and _parsed_next != ""
                _get_next = lambda: self.list(
                    limit=limit,
                    starting_after=_parsed_next,
                    ending_before=ending_before,
                    is_owner=is_owner,
                    is_starred=is_starred,
                    gender=gender,
                    expand=expand,
                    request_options=request_options,
                )
                _items = _parsed_response.data
                return AsyncPager(has_next=_has_next, items=_items, get_next=_get_next)
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def clone(
        self,
        *,
        clip: core.File,
        name: str,
        language: SupportedLanguage,
        mode: CloneMode,
        description: typing.Optional[str] = OMIT,
        enhance: typing.Optional[bool] = OMIT,
        base_voice_id: typing.Optional[VoiceId] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> VoiceMetadata:
        """
        Clone a voice from an audio clip. This endpoint has two modes, stability and similarity.

        Similarity mode clones are more similar to the source clip, but may reproduce background noise. For these, use an audio clip about 5 seconds long.

        Stability mode clones are more stable, but may not sound as similar to the source clip. For these, use an audio clip 10-20 seconds long.

        Parameters
        ----------
        clip : core.File
            See core.File for more documentation

        name : str
            The name of the voice.

        language : SupportedLanguage
            The language of the voice.

        mode : CloneMode
            Tradeoff between similarity and stability. Similarity clones sound more like the source clip, but may reproduce background noise. Stability clones always sound like a studio recording, but may not sound as similar to the source clip.

        description : typing.Optional[str]
            A description for the voice.

        enhance : typing.Optional[bool]
            Whether to apply AI enhancements to the clip to reduce background noise. This leads to cleaner generated speech at the cost of reduced similarity to the source clip.

        base_voice_id : typing.Optional[VoiceId]
            Optional base voice ID that the cloned voice is derived from.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceMetadata

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.clone(
                name="A high-stability cloned voice",
                description="Copied from Cartesia docs",
                mode="stability",
                language="en",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "voices/clone",
            method="POST",
            data={
                "name": name,
                "description": description,
                "language": language,
                "mode": mode,
                "enhance": enhance,
                "base_voice_id": base_voice_id,
            },
            files={
                "clip": clip,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    VoiceMetadata,
                    parse_obj_as(
                        type_=VoiceMetadata,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete(self, id: VoiceId, *, request_options: typing.Optional[RequestOptions] = None) -> None:
        """
        Parameters
        ----------
        id : VoiceId

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        None

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.delete(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"voices/{jsonable_encoder(id)}",
            method="DELETE",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update(
        self, id: VoiceId, *, name: str, description: str, request_options: typing.Optional[RequestOptions] = None
    ) -> Voice:
        """
        Parameters
        ----------
        id : VoiceId

        name : str
            The name of the voice.

        description : str
            The description of the voice.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.update(
                id="id",
                name="name",
                description="description",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"voices/{jsonable_encoder(id)}",
            method="PATCH",
            json={
                "name": name,
                "description": description,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Voice,
                    parse_obj_as(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get(self, id: VoiceId, *, request_options: typing.Optional[RequestOptions] = None) -> Voice:
        """
        Parameters
        ----------
        id : VoiceId

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.get(
                id="id",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            f"voices/{jsonable_encoder(id)}",
            method="GET",
            request_options=request_options,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Voice,
                    parse_obj_as(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def localize(
        self,
        *,
        voice_id: str,
        name: str,
        description: str,
        language: LocalizeTargetLanguage,
        original_speaker_gender: Gender,
        dialect: typing.Optional[LocalizeDialectParams] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> VoiceMetadata:
        """
        Create a new voice from an existing voice localized to a new language and dialect.

        Parameters
        ----------
        voice_id : str
            The ID of the voice to localize.

        name : str
            The name of the new localized voice.

        description : str
            The description of the new localized voice.

        language : LocalizeTargetLanguage

        original_speaker_gender : Gender

        dialect : typing.Optional[LocalizeDialectParams]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        VoiceMetadata

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.localize(
                voice_id="694f9389-aac1-45b6-b726-9d9369183238",
                name="Sarah Peninsular Spanish",
                description="Sarah Voice in Peninsular Spanish",
                language="es",
                original_speaker_gender="female",
                dialect="pe",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "voices/localize",
            method="POST",
            json={
                "voice_id": voice_id,
                "name": name,
                "description": description,
                "language": language,
                "original_speaker_gender": original_speaker_gender,
                "dialect": convert_and_respect_annotation_metadata(
                    object_=dialect, annotation=LocalizeDialectParams, direction="write"
                ),
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    VoiceMetadata,
                    parse_obj_as(
                        type_=VoiceMetadata,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def mix(
        self,
        *,
        voices: typing.Sequence[MixVoiceSpecifierParams],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> EmbeddingResponse:
        """
        Parameters
        ----------
        voices : typing.Sequence[MixVoiceSpecifierParams]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        EmbeddingResponse

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.mix(
                voices=[{"id": "id", "weight": 1.1}, {"id": "id", "weight": 1.1}],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "voices/mix",
            method="POST",
            json={
                "voices": convert_and_respect_annotation_metadata(
                    object_=voices, annotation=typing.Sequence[MixVoiceSpecifierParams], direction="write"
                ),
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    EmbeddingResponse,
                    parse_obj_as(
                        type_=EmbeddingResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create(
        self,
        *,
        name: str,
        description: str,
        embedding: Embedding,
        language: typing.Optional[SupportedLanguage] = OMIT,
        base_voice_id: typing.Optional[BaseVoiceId] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> Voice:
        """
        Create voice from raw features. If you'd like to clone a voice from an audio file, please use Clone Voice instead.

        Parameters
        ----------
        name : str
            The name of the voice.

        description : str
            The description of the voice.

        embedding : Embedding

        language : typing.Optional[SupportedLanguage]

        base_voice_id : typing.Optional[BaseVoiceId]

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        Voice

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voices.create(
                name="name",
                description="description",
                embedding=[1.1, 1.1],
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "voices/",
            method="POST",
            json={
                "name": name,
                "description": description,
                "embedding": embedding,
                "language": language,
                "base_voice_id": base_voice_id,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    Voice,
                    parse_obj_as(
                        type_=Voice,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
