# This file was auto-generated by Fern from our API Definition.

import typing_extensions
from ..types.voice_id import VoiceId
import datetime as dt
from ...tts.types.supported_language import SupportedLanguage


class VoiceMetadataParams(typing_extensions.TypedDict):
    id: VoiceId
    user_id: str
    """
    The ID of the user who owns the voice.
    """

    is_public: bool
    """
    Whether the voice is publicly accessible.
    """

    name: str
    """
    The name of the voice.
    """

    description: str
    """
    The description of the voice.
    """

    created_at: dt.datetime
    """
    The date and time the voice was created.
    """

    language: SupportedLanguage
