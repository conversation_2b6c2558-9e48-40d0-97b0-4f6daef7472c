# This file was auto-generated by Fern from our API Definition.

import typing_extensions
import typing
from .voice import VoiceParams
import typing_extensions
from ..types.voice_id import VoiceId


class GetVoicesResponseParams(typing_extensions.TypedDict):
    data: typing.Sequence[VoiceParams]
    """
    The paginated list of Voices.
    """

    has_more: bool
    """
    Whether there are more Voices to fetch (using `starting_after=id`, where id is the ID of the last Voice in the current response).
    """

    next_page: typing_extensions.NotRequired[VoiceId]
    """
    (Deprecated - use the id of the last Voice in the current response instead.) An ID that can be passed as `starting_after` to get the next page of Voices.
    """
