# This file was auto-generated by Fern from our API Definition.

import typing_extensions
from ..types.localize_target_language import LocalizeTargetLanguage
from ..types.gender import Gender
import typing_extensions
from .localize_dialect import LocalizeDialectParams


class LocalizeVoiceRequestParams(typing_extensions.TypedDict):
    voice_id: str
    """
    The ID of the voice to localize.
    """

    name: str
    """
    The name of the new localized voice.
    """

    description: str
    """
    The description of the new localized voice.
    """

    language: LocalizeTargetLanguage
    original_speaker_gender: Gender
    dialect: typing_extensions.NotRequired[LocalizeDialectParams]
