# This file was auto-generated by Fern from our API Definition.

import typing_extensions
from ...embedding.types.embedding import Embedding
import typing_extensions
from ...tts.types.supported_language import SupportedLanguage
from ..types.base_voice_id import BaseVoiceId


class CreateVoiceRequestParams(typing_extensions.TypedDict):
    name: str
    """
    The name of the voice.
    """

    description: str
    """
    The description of the voice.
    """

    embedding: Embedding
    language: typing_extensions.NotRequired[SupportedLanguage]
    base_voice_id: typing_extensions.NotRequired[BaseVoiceId]
