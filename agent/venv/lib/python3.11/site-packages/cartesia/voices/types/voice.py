# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
from .voice_id import VoiceId
import pydantic
import datetime as dt
import typing
from ...embedding.types.embedding import Embedding
from ...tts.types.supported_language import SupportedLanguage
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class Voice(UniversalBaseModel):
    id: VoiceId
    is_owner: bool = pydantic.Field()
    """
    Whether the current user is the owner of the voice.
    """

    name: str = pydantic.Field()
    """
    The name of the voice.
    """

    description: str = pydantic.Field()
    """
    The description of the voice.
    """

    created_at: dt.datetime = pydantic.Field()
    """
    The date and time the voice was created.
    """

    embedding: typing.Optional[Embedding] = pydantic.Field(default=None)
    """
    The vector embedding of the voice. Only included when `expand` includes `embedding`.
    """

    is_starred: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether the current user has starred the voice. Only included when `expand` includes `is_starred`.
    """

    language: SupportedLanguage

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
