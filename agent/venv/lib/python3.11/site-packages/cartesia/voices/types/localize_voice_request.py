# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
from .localize_target_language import LocalizeTargetLanguage
from .gender import Gender
import typing
from .localize_dialect import LocalizeDialect
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class LocalizeVoiceRequest(UniversalBaseModel):
    voice_id: str = pydantic.Field()
    """
    The ID of the voice to localize.
    """

    name: str = pydantic.Field()
    """
    The name of the new localized voice.
    """

    description: str = pydantic.Field()
    """
    The description of the new localized voice.
    """

    language: LocalizeTargetLanguage
    original_speaker_gender: Gender
    dialect: typing.Optional[LocalizeDialect] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
