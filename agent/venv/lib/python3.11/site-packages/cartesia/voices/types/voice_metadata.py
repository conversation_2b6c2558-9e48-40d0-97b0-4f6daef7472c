# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
from .voice_id import VoiceId
import pydantic
import datetime as dt
from ...tts.types.supported_language import SupportedLanguage
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import typing


class VoiceMetadata(UniversalBaseModel):
    id: VoiceId
    user_id: str = pydantic.Field()
    """
    The ID of the user who owns the voice.
    """

    is_public: bool = pydantic.Field()
    """
    Whether the voice is publicly accessible.
    """

    name: str = pydantic.Field()
    """
    The name of the voice.
    """

    description: str = pydantic.Field()
    """
    The description of the voice.
    """

    created_at: dt.datetime = pydantic.Field()
    """
    The date and time the voice was created.
    """

    language: SupportedLanguage

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
