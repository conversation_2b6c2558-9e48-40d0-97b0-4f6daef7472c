# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
from ...embedding.types.embedding import Embedding
import typing
from ...tts.types.supported_language import SupportedLanguage
from .base_voice_id import BaseVoiceId
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class CreateVoiceRequest(UniversalBaseModel):
    name: str = pydantic.Field()
    """
    The name of the voice.
    """

    description: str = pydantic.Field()
    """
    The description of the voice.
    """

    embedding: Embedding
    language: typing.Optional[SupportedLanguage] = None
    base_voice_id: typing.Optional[BaseVoiceId] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
