# This file was auto-generated by Fern from our API Definition.

import typing_extensions
from .tts_request_voice_specifier import TtsRequestVoiceSpecifierParams
import typing_extensions
from ..types.supported_language import SupportedLanguage
from .output_format import OutputFormatParams
from ..types.model_speed import ModelSpeed


class TtsRequestParams(typing_extensions.TypedDict):
    model_id: str
    """
    The ID of the model to use for the generation. See [Models](/build-with-cartesia/models) for available models.
    """

    transcript: str
    voice: TtsRequestVoiceSpecifierParams
    language: typing_extensions.NotRequired[SupportedLanguage]
    output_format: OutputFormatParams
    duration: typing_extensions.NotRequired[float]
    """
    The maximum duration of the audio in seconds. You do not usually need to specify this.
    If the duration is not appropriate for the length of the transcript, the output audio may be truncated.
    """

    speed: typing_extensions.NotRequired[ModelSpeed]
