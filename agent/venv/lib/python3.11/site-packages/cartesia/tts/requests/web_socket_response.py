# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
import typing_extensions
import typing
import typing_extensions
from ..types.context_id import ContextId
from ..types.flush_id import FlushId
from .word_timestamps import WordTimestampsParams
from .phoneme_timestamps import PhonemeTimestampsParams


class WebSocketResponse_ChunkParams(typing_extensions.TypedDict):
    type: typing.Literal["chunk"]
    data: str
    step_time: float
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


class WebSocketResponse_FlushDoneParams(typing_extensions.TypedDict):
    type: typing.Literal["flush_done"]
    flush_id: FlushId
    flush_done: bool
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


class WebSocketResponse_DoneParams(typing_extensions.TypedDict):
    type: typing.Literal["done"]
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


class WebSocketResponse_TimestampsParams(typing_extensions.TypedDict):
    type: typing.Literal["timestamps"]
    word_timestamps: typing_extensions.NotRequired[WordTimestampsParams]
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


class WebSocketResponse_ErrorParams(typing_extensions.TypedDict):
    type: typing.Literal["error"]
    error: str
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


class WebSocketResponse_PhonemeTimestampsParams(typing_extensions.TypedDict):
    type: typing.Literal["phoneme_timestamps"]
    phoneme_timestamps: typing_extensions.NotRequired[PhonemeTimestampsParams]
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


WebSocketResponseParams = typing.Union[
    WebSocketResponse_ChunkParams,
    WebSocketResponse_FlushDoneParams,
    WebSocketResponse_DoneParams,
    WebSocketResponse_TimestampsParams,
    WebSocketResponse_ErrorParams,
    WebSocketResponse_PhonemeTimestampsParams,
]
