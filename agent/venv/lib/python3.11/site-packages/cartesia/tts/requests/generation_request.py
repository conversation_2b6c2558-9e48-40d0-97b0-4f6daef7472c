# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing_extensions
import typing
from .tts_request_voice_specifier import TtsRequestVoiceSpecifierParams
import typing_extensions
from ..types.supported_language import SupportedLanguage
from .web_socket_raw_output_format import WebSocketRawOutputFormatParams
from ..types.model_speed import ModelSpeed
from ..types.context_id import ContextId
from ...core.serialization import FieldMetadata


class GenerationRequestParams(typing_extensions.TypedDict):
    model_id: str
    """
    The ID of the model to use for the generation. See [Models](/build-with-cartesia/models) for available models.
    """

    transcript: typing.Optional[typing.Any]
    """
    The transcript to generate speech for. This can be a string or an iterator over strings.
    """

    voice: TtsRequestVoiceSpecifierParams
    language: typing_extensions.NotRequired[SupportedLanguage]
    output_format: WebSocketRawOutputFormatParams
    duration: typing_extensions.NotRequired[float]
    """
    The maximum duration of the audio in seconds. You do not usually need to specify this.
    If the duration is not appropriate for the length of the transcript, the output audio may be truncated.
    """

    speed: typing_extensions.NotRequired[ModelSpeed]
    context_id: typing_extensions.NotRequired[ContextId]
    continue_: typing_extensions.NotRequired[typing_extensions.Annotated[bool, FieldMetadata(alias="continue")]]
    """
    Whether this input may be followed by more inputs.
    If not specified, this defaults to `false`.
    """

    max_buffer_delay_ms: typing_extensions.NotRequired[int]
    """
    The maximum time in milliseconds to buffer text before starting generation. Values between [0, 1000]ms are supported. Defaults to 0 (no buffering).
    
    When set, the model will buffer incoming text chunks until it's confident it has enough context to generate high-quality speech, or the buffer delay elapses, whichever comes first. Without this option set, the model will kick off generations immediately, ceding control of buffering to the user.
    
    Use this to balance responsiveness with higher quality speech generation, which often benefits from having more context.
    """

    flush: typing_extensions.NotRequired[bool]
    """
    Whether to flush the context.
    """

    add_timestamps: typing_extensions.NotRequired[bool]
    """
    Whether to return word-level timestamps. If `false` (default), no word timestamps will be produced at all. If `true`, the server will return timestamp events containing word-level timing information.
    """

    add_phoneme_timestamps: typing_extensions.NotRequired[bool]
    """
    Whether to return phoneme-level timestamps. If `false` (default), no phoneme timestamps will be produced. If `true`, the server will return timestamp events containing phoneme-level timing information.
    """

    use_normalized_timestamps: typing_extensions.NotRequired[bool]
    """
    Whether to use normalized timestamps (True) or original timestamps (False).
    """
