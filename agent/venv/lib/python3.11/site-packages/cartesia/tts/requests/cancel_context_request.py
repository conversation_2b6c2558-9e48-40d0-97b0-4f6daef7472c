# This file was auto-generated by Fern from our API Definition.

import typing_extensions
from ..types.context_id import ContextId
import typing


class CancelContextRequestParams(typing_extensions.TypedDict):
    context_id: ContextId
    """
    The ID of the context to cancel.
    """

    cancel: typing.Literal[True]
    """
    Whether to cancel the context, so that no more messages are generated for that context.
    """
