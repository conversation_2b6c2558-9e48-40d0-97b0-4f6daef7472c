# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing_extensions
from .tts_request_voice_specifier import TtsRequestVoiceSpecifierParams
import typing_extensions
from ..types.supported_language import SupportedLanguage
from .sse_output_format import SseOutputFormatParams
from ..types.model_speed import ModelSpeed
from ..types.context_id import ContextId


class TtssseRequestParams(typing_extensions.TypedDict):
    model_id: str
    """
    The ID of the model to use for the generation. See [Models](/build-with-cartesia/models) for available models.
    """

    transcript: str
    voice: TtsRequestVoiceSpecifierParams
    language: typing_extensions.NotRequired[SupportedLanguage]
    output_format: SseOutputFormatParams
    duration: typing_extensions.NotRequired[float]
    """
    The maximum duration of the audio in seconds. You do not usually need to specify this.
    If the duration is not appropriate for the length of the transcript, the output audio may be truncated.
    """

    speed: typing_extensions.NotRequired[ModelSpeed]
    add_timestamps: typing_extensions.NotRequired[bool]
    """
    Whether to return word-level timestamps. If `false` (default), no word timestamps will be produced at all. If `true`, the server will return timestamp events containing word-level timing information.
    """

    add_phoneme_timestamps: typing_extensions.NotRequired[bool]
    """
    Whether to return phoneme-level timestamps. If `false` (default), no phoneme timestamps will be produced - if `add_timestamps` is `true`, the produced timestamps will be word timestamps instead. If `true`, the server will return timestamp events containing phoneme-level timing information.
    """

    use_normalized_timestamps: typing_extensions.NotRequired[bool]
    """
    Whether to use normalized timestamps (True) or original timestamps (False).
    """

    context_id: typing_extensions.NotRequired[ContextId]
    """
    Optional context ID for this request.
    """
