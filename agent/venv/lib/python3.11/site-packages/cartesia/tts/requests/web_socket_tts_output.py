# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing_extensions
import typing_extensions
from .word_timestamps import WordTimestampsParams
from .phoneme_timestamps import PhonemeTimestampsParams
import typing
from ..types.context_id import ContextId
from ..types.flush_id import FlushId


class WebSocketTtsOutputParams(typing_extensions.TypedDict):
    word_timestamps: typing_extensions.NotRequired[WordTimestampsParams]
    phoneme_timestamps: typing_extensions.NotRequired[PhonemeTimestampsParams]
    audio: typing_extensions.NotRequired[typing.Optional[typing.Any]]
    context_id: typing_extensions.NotRequired[ContextId]
    flush_id: typing_extensions.NotRequired[FlushId]
    flush_done: typing_extensions.NotRequired[bool]
