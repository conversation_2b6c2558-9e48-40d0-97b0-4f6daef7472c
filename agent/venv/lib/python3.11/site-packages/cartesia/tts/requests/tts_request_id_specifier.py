# This file was auto-generated by Fern from our API Definition.

import typing_extensions
import typing
from ...voices.types.voice_id import VoiceId
import typing_extensions
from .controls import ControlsParams
from ...core.serialization import FieldMetadata


class TtsRequestIdSpecifierParams(typing_extensions.TypedDict):
    mode: typing.Literal["id"]
    id: VoiceId
    experimental_controls: typing_extensions.NotRequired[
        typing_extensions.Annotated[ControlsParams, FieldMetadata(alias="__experimental_controls")]
    ]
