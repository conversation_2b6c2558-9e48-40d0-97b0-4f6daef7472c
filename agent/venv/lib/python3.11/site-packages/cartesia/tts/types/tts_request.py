# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
from .tts_request_voice_specifier import TtsRequestVoiceSpecifier
import typing
from .supported_language import SupportedLanguage
from .output_format import OutputFormat
from .model_speed import ModelSpeed
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class TtsRequest(UniversalBaseModel):
    model_id: str = pydantic.Field()
    """
    The ID of the model to use for the generation. See [Models](/build-with-cartesia/models) for available models.
    """

    transcript: str
    voice: TtsRequestVoiceSpecifier
    language: typing.Optional[SupportedLanguage] = None
    output_format: OutputFormat
    duration: typing.Optional[float] = pydantic.Field(default=None)
    """
    The maximum duration of the audio in seconds. You do not usually need to specify this.
    If the duration is not appropriate for the length of the transcript, the output audio may be truncated.
    """

    speed: typing.Optional[ModelSpeed] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
