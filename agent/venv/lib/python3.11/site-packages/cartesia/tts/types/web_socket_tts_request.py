# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
import typing
from .output_format import OutputFormat
from .tts_request_voice_specifier import TtsRequestVoiceSpecifier
import typing_extensions
from ...core.serialization import FieldMetadata
from .model_speed import ModelSpeed
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class WebSocketTtsRequest(UniversalBaseModel):
    model_id: str = pydantic.Field()
    """
    The ID of the model to use for the generation. See [Models](/build-with-cartesia/models) for available models.
    """

    output_format: typing.Optional[OutputFormat] = None
    transcript: typing.Optional[str] = None
    voice: TtsRequestVoiceSpecifier
    duration: typing.Optional[int] = None
    language: typing.Optional[str] = None
    add_timestamps: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to return word-level timestamps. If `false` (default), no word timestamps will be produced at all. If `true`, the server will return timestamp events containing word-level timing information.
    """

    add_phoneme_timestamps: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to return phoneme-level timestamps. If `false` (default), no phoneme timestamps will be produced - if `add_timestamps` is `true`, the produced timestamps will be word timestamps instead. If `true`, the server will return timestamp events containing phoneme-level timing information.
    """

    use_normalized_timestamps: typing.Optional[bool] = None
    continue_: typing_extensions.Annotated[typing.Optional[bool], FieldMetadata(alias="continue")] = None
    context_id: typing.Optional[str] = None
    max_buffer_delay_ms: typing.Optional[int] = None
    speed: typing.Optional[ModelSpeed] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
