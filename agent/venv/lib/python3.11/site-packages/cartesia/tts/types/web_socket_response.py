# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ...core.pydantic_utilities import UniversalBaseModel
import typing
from .context_id import ContextId
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic
from .flush_id import FlushId
from .word_timestamps import WordTimestamps
from .phoneme_timestamps import PhonemeTimestamps


class WebSocketResponse_Chunk(UniversalBaseModel):
    type: typing.Literal["chunk"] = "chunk"
    data: str
    step_time: float
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class WebSocketResponse_FlushDone(UniversalBaseModel):
    type: typing.Literal["flush_done"] = "flush_done"
    flush_id: FlushId
    flush_done: bool
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class WebSocketResponse_Done(UniversalBaseModel):
    type: typing.Literal["done"] = "done"
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class WebSocketResponse_Timestamps(UniversalBaseModel):
    type: typing.Literal["timestamps"] = "timestamps"
    word_timestamps: typing.Optional[WordTimestamps] = None
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class WebSocketResponse_Error(UniversalBaseModel):
    type: typing.Literal["error"] = "error"
    error: str
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class WebSocketResponse_PhonemeTimestamps(UniversalBaseModel):
    type: typing.Literal["phoneme_timestamps"] = "phoneme_timestamps"
    phoneme_timestamps: typing.Optional[PhonemeTimestamps] = None
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


WebSocketResponse = typing.Union[
    WebSocketResponse_Chunk,
    WebSocketResponse_FlushDone,
    WebSocketResponse_Done,
    WebSocketResponse_Timestamps,
    WebSocketResponse_Error,
    WebSocketResponse_PhonemeTimestamps,
]
