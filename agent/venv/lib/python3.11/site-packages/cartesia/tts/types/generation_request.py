# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
import typing
from .tts_request_voice_specifier import TtsRequestVoiceSpecifier
from .supported_language import SupportedLanguage
from .web_socket_raw_output_format import WebSocketRawOutputFormat
from .model_speed import ModelSpeed
from .context_id import ContextId
import typing_extensions
from ...core.serialization import FieldMetadata
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class GenerationRequest(UniversalBaseModel):
    model_id: str = pydantic.Field()
    """
    The ID of the model to use for the generation. See [Models](/build-with-cartesia/models) for available models.
    """

    transcript: typing.Optional[typing.Any] = pydantic.Field(default=None)
    """
    The transcript to generate speech for. This can be a string or an iterator over strings.
    """

    voice: TtsRequestVoiceSpecifier
    language: typing.Optional[SupportedLanguage] = None
    output_format: WebSocketRawOutputFormat
    duration: typing.Optional[float] = pydantic.Field(default=None)
    """
    The maximum duration of the audio in seconds. You do not usually need to specify this.
    If the duration is not appropriate for the length of the transcript, the output audio may be truncated.
    """

    speed: typing.Optional[ModelSpeed] = None
    context_id: typing.Optional[ContextId] = None
    continue_: typing_extensions.Annotated[typing.Optional[bool], FieldMetadata(alias="continue")] = pydantic.Field(
        default=None
    )
    """
    Whether this input may be followed by more inputs.
    If not specified, this defaults to `false`.
    """

    max_buffer_delay_ms: typing.Optional[int] = pydantic.Field(default=None)
    """
    The maximum time in milliseconds to buffer text before starting generation. Values between [0, 1000]ms are supported. Defaults to 0 (no buffering).
    
    When set, the model will buffer incoming text chunks until it's confident it has enough context to generate high-quality speech, or the buffer delay elapses, whichever comes first. Without this option set, the model will kick off generations immediately, ceding control of buffering to the user.
    
    Use this to balance responsiveness with higher quality speech generation, which often benefits from having more context.
    """

    flush: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to flush the context.
    """

    add_timestamps: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to return word-level timestamps. If `false` (default), no word timestamps will be produced at all. If `true`, the server will return timestamp events containing word-level timing information.
    """

    add_phoneme_timestamps: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to return phoneme-level timestamps. If `false` (default), no phoneme timestamps will be produced. If `true`, the server will return timestamp events containing phoneme-level timing information.
    """

    use_normalized_timestamps: typing.Optional[bool] = pydantic.Field(default=None)
    """
    Whether to use normalized timestamps (True) or original timestamps (False).
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
