# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import typing
from ...voices.types.voice_id import VoiceId
import typing_extensions
from .controls import Controls
from ...core.serialization import FieldMetadata
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class TtsRequestIdSpecifier(UniversalBaseModel):
    mode: typing.Literal["id"] = "id"
    id: VoiceId
    experimental_controls: typing_extensions.Annotated[
        typing.Optional[Controls], FieldMetadata(alias="__experimental_controls")
    ] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
