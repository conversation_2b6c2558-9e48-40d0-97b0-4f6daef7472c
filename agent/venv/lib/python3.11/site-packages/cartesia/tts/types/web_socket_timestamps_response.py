# This file was auto-generated by Fern from our API Definition.

from .web_socket_base_response import WebSocketBaseResponse
import typing
from .word_timestamps import WordTimestamps
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class WebSocketTimestampsResponse(WebSocketBaseResponse):
    word_timestamps: typing.Optional[WordTimestamps] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
