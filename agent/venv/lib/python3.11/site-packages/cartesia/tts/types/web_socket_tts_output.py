# This file was auto-generated by <PERSON>rn from our API Definition.

import typing

import pydantic

from ...core.pydantic_utilities import IS_PYDANTIC_V2, UniversalBaseModel
from .context_id import ContextId
from .flush_id import FlushId
from .phoneme_timestamps import PhonemeTimestamps
from .word_timestamps import WordTimestamps


class WebSocketTtsOutput(UniversalBaseModel):
    word_timestamps: typing.Optional[WordTimestamps] = None
    phoneme_timestamps: typing.Optional[PhonemeTimestamps] = None
    audio: typing.Optional[bytes] = None
    context_id: typing.Optional[ContextId] = None
    flush_id: typing.Optional[FlushId] = None
    flush_done: typing.Optional[bool] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
