# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
from .context_id import ContextId
import pydantic
import typing
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class CancelContextRequest(UniversalBaseModel):
    context_id: ContextId = pydantic.Field()
    """
    The ID of the context to cancel.
    """

    cancel: typing.Literal[True] = pydantic.Field(default=True)
    """
    Whether to cancel the context, so that no more messages are generated for that context.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
