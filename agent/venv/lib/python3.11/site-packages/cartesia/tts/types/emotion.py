# This file was auto-generated by Fern from our API Definition.

import typing

Emotion = typing.Union[
    typing.Literal[
        "anger:lowest",
        "anger:low",
        "anger",
        "anger:high",
        "anger:highest",
        "positivity:lowest",
        "positivity:low",
        "positivity",
        "positivity:high",
        "positivity:highest",
        "surprise:lowest",
        "surprise:low",
        "surprise",
        "surprise:high",
        "surprise:highest",
        "sadness:lowest",
        "sadness:low",
        "sadness",
        "sadness:high",
        "sadness:highest",
        "curiosity:lowest",
        "curiosity:low",
        "curiosity",
        "curiosity:high",
        "curiosity:highest",
    ],
    typing.Any,
]
