# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import typing


class Mp3OutputFormat(UniversalBaseModel):
    sample_rate: int
    bit_rate: int = pydantic.Field()
    """
    The bit rate of the audio in bits per second. Supported bit rates are 32000, 64000, 96000, 128000, 192000.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
