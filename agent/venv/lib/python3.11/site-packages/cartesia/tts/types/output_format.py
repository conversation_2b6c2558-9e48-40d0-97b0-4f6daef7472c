# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations
from ...core.pydantic_utilities import UniversalBaseModel
import typing
from .raw_encoding import RawEncoding
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class OutputFormat_Raw(UniversalBaseModel):
    container: typing.Literal["raw"] = "raw"
    encoding: RawEncoding
    sample_rate: int
    bit_rate: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class OutputFormat_Wav(UniversalBaseModel):
    container: typing.Literal["wav"] = "wav"
    encoding: RawEncoding
    sample_rate: int
    bit_rate: typing.Optional[int] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class OutputFormat_Mp3(UniversalBaseModel):
    container: typing.Literal["mp3"] = "mp3"
    sample_rate: int
    bit_rate: int

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


OutputFormat = typing.Union[OutputFormat_Raw, OutputFormat_Wav, OutputFormat_Mp3]
