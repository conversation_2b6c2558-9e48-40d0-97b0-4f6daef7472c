# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import typing
from .raw_encoding import RawEncoding
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class WebSocketRawOutputFormat(UniversalBaseModel):
    container: typing.Literal["raw"] = "raw"
    encoding: RawEncoding
    sample_rate: int

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
