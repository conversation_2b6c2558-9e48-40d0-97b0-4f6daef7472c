# This file was auto-generated by Fern from our API Definition.

import typing_extensions
import typing_extensions


class TokenGrantParams(typing_extensions.TypedDict):
    tts: typing_extensions.NotRequired[bool]
    """
    The `tts` grant allows the token to be used to access any TTS endpoint.
    """

    stt: typing_extensions.NotRequired[bool]
    """
    The `stt` grant allows the token to be used to access any STT endpoint.
    """
