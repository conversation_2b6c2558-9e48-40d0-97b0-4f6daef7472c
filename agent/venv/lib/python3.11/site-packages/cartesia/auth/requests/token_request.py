# This file was auto-generated by Fern from our API Definition.

import typing_extensions
import typing_extensions
from .token_grant import TokenGrantParams


class TokenRequestParams(typing_extensions.TypedDict):
    grants: typing_extensions.NotRequired[TokenGrantParams]
    """
    The permissions to be granted via the token. Both TTS and STT grants are optional - specify only the capabilities you need.
    """

    expires_in: typing_extensions.NotRequired[int]
    """
    The number of seconds the token will be valid for since the time of generation. The maximum is 1 hour (3600 seconds).
    """
