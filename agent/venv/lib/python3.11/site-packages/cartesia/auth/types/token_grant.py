# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import typing
import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class TokenGrant(UniversalBaseModel):
    tts: typing.Optional[bool] = pydantic.Field(default=None)
    """
    The `tts` grant allows the token to be used to access any TTS endpoint.
    """

    stt: typing.Optional[bool] = pydantic.Field(default=None)
    """
    The `stt` grant allows the token to be used to access any STT endpoint.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
