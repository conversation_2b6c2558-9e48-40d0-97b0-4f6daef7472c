# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import typing
from .token_grant import TokenGrant
import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class TokenRequest(UniversalBaseModel):
    grants: typing.Optional[TokenGrant] = pydantic.Field(default=None)
    """
    The permissions to be granted via the token. Both TTS and STT grants are optional - specify only the capabilities you need.
    """

    expires_in: typing.Optional[int] = pydantic.Field(default=None)
    """
    The number of seconds the token will be valid for since the time of generation. The maximum is 1 hour (3600 seconds).
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
