# This file was auto-generated by Fern from our API Definition.

import typing_extensions
import typing_extensions
import typing
from .transcription_word import TranscriptionWordParams


class TranscriptMessageParams(typing_extensions.TypedDict):
    request_id: str
    """
    Unique identifier for this transcription session.
    """

    text: str
    """
    The transcribed text. May be partial or final depending on is_final.
    
    **Note**: Text may be empty in initial responses while the system accumulates sufficient audio for transcription. This is normal behavior - wait for responses with non-empty text or monitor is_final for completion status.
    """

    is_final: bool
    """
    Whether this is a final transcription result or an interim result.
    """

    duration: typing_extensions.NotRequired[float]
    """
    The duration of the audio transcribed so far, in seconds.
    """

    language: typing_extensions.NotRequired[str]
    """
    The specified language of the input audio.
    """

    words: typing_extensions.NotRequired[typing.Sequence[TranscriptionWordParams]]
    """
    Word-level timestamps showing the start and end time of each word in seconds. Always included in streaming responses.
    """
