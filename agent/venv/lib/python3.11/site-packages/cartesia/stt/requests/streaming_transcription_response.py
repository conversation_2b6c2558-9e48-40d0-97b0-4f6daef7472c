# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
import typing_extensions
import typing
import typing_extensions
from .transcription_word import TranscriptionWordParams


class StreamingTranscriptionResponse_TranscriptParams(typing_extensions.TypedDict):
    type: typing.Literal["transcript"]
    request_id: str
    text: str
    is_final: bool
    duration: typing_extensions.NotRequired[float]
    language: typing_extensions.NotRequired[str]
    words: typing_extensions.NotRequired[typing.Sequence[TranscriptionWordParams]]


class StreamingTranscriptionResponse_FlushDoneParams(typing_extensions.TypedDict):
    type: typing.Literal["flush_done"]
    request_id: str


class StreamingTranscriptionResponse_DoneParams(typing_extensions.TypedDict):
    type: typing.Literal["done"]
    request_id: str


class StreamingTranscriptionResponse_ErrorParams(typing_extensions.TypedDict):
    type: typing.Literal["error"]
    request_id: typing_extensions.NotRequired[str]
    message: str


StreamingTranscriptionResponseParams = typing.Union[
    StreamingTranscriptionResponse_TranscriptParams,
    StreamingTranscriptionResponse_FlushDoneParams,
    StreamingTranscriptionResponse_DoneParams,
    StreamingTranscriptionResponse_ErrorParams,
]
