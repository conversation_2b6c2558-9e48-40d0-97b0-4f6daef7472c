# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
import typing
from .transcription_word import TranscriptionWord
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class TranscriptMessage(UniversalBaseModel):
    request_id: str = pydantic.Field()
    """
    Unique identifier for this transcription session.
    """

    text: str = pydantic.Field()
    """
    The transcribed text. May be partial or final depending on is_final.
    
    **Note**: Text may be empty in initial responses while the system accumulates sufficient audio for transcription. This is normal behavior - wait for responses with non-empty text or monitor is_final for completion status.
    """

    is_final: bool = pydantic.Field()
    """
    Whether this is a final transcription result or an interim result.
    """

    duration: typing.Optional[float] = pydantic.Field(default=None)
    """
    The duration of the audio transcribed so far, in seconds.
    """

    language: typing.Optional[str] = pydantic.Field(default=None)
    """
    The specified language of the input audio.
    """

    words: typing.Optional[typing.List[TranscriptionWord]] = pydantic.Field(default=None)
    """
    Word-level timestamps showing the start and end time of each word in seconds. Always included in streaming responses.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
