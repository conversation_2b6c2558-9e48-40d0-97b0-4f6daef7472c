# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
import typing
from .transcription_word import TranscriptionWord
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class TranscriptionResponse(UniversalBaseModel):
    text: str = pydantic.Field()
    """
    The transcribed text.
    """

    language: typing.Optional[str] = pydantic.Field(default=None)
    """
    The specified language of the input audio.
    """

    duration: typing.Optional[float] = pydantic.Field(default=None)
    """
    The duration of the input audio in seconds.
    """

    words: typing.Optional[typing.List[TranscriptionWord]] = pydantic.Field(default=None)
    """
    Word-level timestamps showing the start and end time of each word. Only included when `[word]` is passed into `timestamp_granularities[]`.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
