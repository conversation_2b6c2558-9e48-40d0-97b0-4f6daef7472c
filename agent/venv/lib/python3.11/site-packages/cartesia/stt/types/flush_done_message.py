# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import typing


class FlushDoneMessage(UniversalBaseModel):
    """
    Acknowledgment message sent in response to a `finalize` command, indicating that all buffered audio has been flushed and processed.
    """

    request_id: str = pydantic.Field()
    """
    Unique identifier for this transcription session.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
