# This file was auto-generated by Fern from our API Definition.

from ...core.pydantic_utilities import UniversalBaseModel
import typing
import pydantic
from ...core.pydantic_utilities import IS_PYDANTIC_V2


class ErrorMessage(UniversalBaseModel):
    request_id: typing.Optional[str] = pydantic.Field(default=None)
    """
    The request ID associated with the error, if applicable.
    """

    message: str = pydantic.Field()
    """
    Human-readable error message describing what went wrong.
    """

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow
