# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
from ...core.pydantic_utilities import UniversalBaseModel
import typing
from .transcription_word import TranscriptionWord
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class StreamingTranscriptionResponse_Transcript(UniversalBaseModel):
    """
    The server sends transcription results, control messages, or error messages. Each message has a `type` field to distinguish between different message types.
    """

    type: typing.Literal["transcript"] = "transcript"
    request_id: str
    text: str
    is_final: bool
    duration: typing.Optional[float] = None
    language: typing.Optional[str] = None
    words: typing.Optional[typing.List[TranscriptionWord]] = None

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class StreamingTranscriptionResponse_FlushDone(UniversalBaseModel):
    """
    The server sends transcription results, control messages, or error messages. Each message has a `type` field to distinguish between different message types.
    """

    type: typing.Literal["flush_done"] = "flush_done"
    request_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class StreamingTranscriptionResponse_Done(UniversalBaseModel):
    """
    The server sends transcription results, control messages, or error messages. Each message has a `type` field to distinguish between different message types.
    """

    type: typing.Literal["done"] = "done"
    request_id: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class StreamingTranscriptionResponse_Error(UniversalBaseModel):
    """
    The server sends transcription results, control messages, or error messages. Each message has a `type` field to distinguish between different message types.
    """

    type: typing.Literal["error"] = "error"
    request_id: typing.Optional[str] = None
    message: str

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


StreamingTranscriptionResponse = typing.Union[
    StreamingTranscriptionResponse_Transcript,
    StreamingTranscriptionResponse_FlushDone,
    StreamingTranscriptionResponse_Done,
    StreamingTranscriptionResponse_Error,
]
