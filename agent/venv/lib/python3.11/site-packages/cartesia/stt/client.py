# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from ..core.client_wrapper import SyncClientWrapper
from .. import core
from .types.stt_encoding import SttEncoding
from .types.timestamp_granularity import TimestampGranularity
from ..core.request_options import RequestOptions
from .types.transcription_response import TranscriptionResponse
from ..core.pydantic_utilities import parse_obj_as
from json.decoder import J<PERSON><PERSON>ecode<PERSON>rror
from ..core.api_error import ApiError
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class SttClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def transcribe(
        self,
        *,
        file: core.File,
        model: str,
        encoding: typing.Optional[SttEncoding] = None,
        sample_rate: typing.Optional[int] = None,
        language: typing.Optional[str] = OMIT,
        timestamp_granularities: typing.Optional[typing.List[TimestampGranularity]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> TranscriptionResponse:
        """
        Transcribes audio files into text using Cartesia's Speech-to-Text API.

        Upload an audio file and receive a complete transcription response. Supports arbitrarily long audio files with automatic intelligent chunking for longer audio.

        **Supported audio formats:** flac, m4a, mp3, mp4, mpeg, mpga, oga, ogg, wav, webm

        **Response format:** Returns JSON with transcribed text, duration, and language. Include `timestamp_granularities: ["word"]` to get word-level timestamps.

        **Pricing:** Batch transcription is priced at **1 credit per 2 seconds** of audio processed.

        <Note>
        For migrating from the OpenAI SDK, see our [OpenAI Whisper to Cartesia Ink Migration Guide](/api-reference/stt/migrate-from-open-ai).
        </Note>

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        model : str
            ID of the model to use for transcription. Use `ink-whisper` for the latest Cartesia Whisper model.

        encoding : typing.Optional[SttEncoding]
            The encoding format to process the audio as. If not specified, the audio file will be decoded automatically.

            **Supported formats:**
            - `pcm_s16le` - 16-bit signed integer PCM, little-endian (recommended for best performance)
            - `pcm_s32le` - 32-bit signed integer PCM, little-endian
            - `pcm_f16le` - 16-bit floating point PCM, little-endian
            - `pcm_f32le` - 32-bit floating point PCM, little-endian
            - `pcm_mulaw` - 8-bit μ-law encoded PCM
            - `pcm_alaw` - 8-bit A-law encoded PCM

        sample_rate : typing.Optional[int]
            The sample rate of the audio in Hz.

        language : typing.Optional[str]
            The language of the input audio in ISO-639-1 format. Defaults to `en`.

            <Accordion title="Supported languages">
              - `en` (English)
              - `zh` (Chinese)
              - `de` (German)
              - `es` (Spanish)
              - `ru` (Russian)
              - `ko` (Korean)
              - `fr` (French)
              - `ja` (Japanese)
              - `pt` (Portuguese)
              - `tr` (Turkish)
              - `pl` (Polish)
              - `ca` (Catalan)
              - `nl` (Dutch)
              - `ar` (Arabic)
              - `sv` (Swedish)
              - `it` (Italian)
              - `id` (Indonesian)
              - `hi` (Hindi)
              - `fi` (Finnish)
              - `vi` (Vietnamese)
              - `he` (Hebrew)
              - `uk` (Ukrainian)
              - `el` (Greek)
              - `ms` (Malay)
              - `cs` (Czech)
              - `ro` (Romanian)
              - `da` (Danish)
              - `hu` (Hungarian)
              - `ta` (Tamil)
              - `no` (Norwegian)
              - `th` (Thai)
              - `ur` (Urdu)
              - `hr` (Croatian)
              - `bg` (Bulgarian)
              - `lt` (Lithuanian)
              - `la` (Latin)
              - `mi` (Maori)
              - `ml` (Malayalam)
              - `cy` (Welsh)
              - `sk` (Slovak)
              - `te` (Telugu)
              - `fa` (Persian)
              - `lv` (Latvian)
              - `bn` (Bengali)
              - `sr` (Serbian)
              - `az` (Azerbaijani)
              - `sl` (Slovenian)
              - `kn` (Kannada)
              - `et` (Estonian)
              - `mk` (Macedonian)
              - `br` (Breton)
              - `eu` (Basque)
              - `is` (Icelandic)
              - `hy` (Armenian)
              - `ne` (Nepali)
              - `mn` (Mongolian)
              - `bs` (Bosnian)
              - `kk` (Kazakh)
              - `sq` (Albanian)
              - `sw` (Swahili)
              - `gl` (Galician)
              - `mr` (Marathi)
              - `pa` (Punjabi)
              - `si` (Sinhala)
              - `km` (Khmer)
              - `sn` (Shona)
              - `yo` (Yoruba)
              - `so` (Somali)
              - `af` (Afrikaans)
              - `oc` (Occitan)
              - `ka` (Georgian)
              - `be` (Belarusian)
              - `tg` (Tajik)
              - `sd` (Sindhi)
              - `gu` (Gujarati)
              - `am` (Amharic)
              - `yi` (Yiddish)
              - `lo` (Lao)
              - `uz` (Uzbek)
              - `fo` (Faroese)
              - `ht` (Haitian Creole)
              - `ps` (Pashto)
              - `tk` (Turkmen)
              - `nn` (Nynorsk)
              - `mt` (Maltese)
              - `sa` (Sanskrit)
              - `lb` (Luxembourgish)
              - `my` (Myanmar)
              - `bo` (Tibetan)
              - `tl` (Tagalog)
              - `mg` (Malagasy)
              - `as` (Assamese)
              - `tt` (Tatar)
              - `haw` (Hawaiian)
              - `ln` (Lingala)
              - `ha` (Hausa)
              - `ba` (Bashkir)
              - `jw` (Javanese)
              - `su` (Sundanese)
              - `yue` (Cantonese)
            </Accordion>

        timestamp_granularities : typing.Optional[typing.List[TimestampGranularity]]
            The timestamp granularities to populate for this transcription. Currently only `word` level timestamps are supported.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TranscriptionResponse

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.stt.transcribe(
            model="ink-whisper",
            language="en",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "stt",
            method="POST",
            params={
                "encoding": encoding,
                "sample_rate": sample_rate,
            },
            data={
                "model": model,
                "language": language,
                "timestamp_granularities[]": timestamp_granularities,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    TranscriptionResponse,
                    parse_obj_as(
                        type_=TranscriptionResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncSttClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def transcribe(
        self,
        *,
        file: core.File,
        model: str,
        encoding: typing.Optional[SttEncoding] = None,
        sample_rate: typing.Optional[int] = None,
        language: typing.Optional[str] = OMIT,
        timestamp_granularities: typing.Optional[typing.List[TimestampGranularity]] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> TranscriptionResponse:
        """
        Transcribes audio files into text using Cartesia's Speech-to-Text API.

        Upload an audio file and receive a complete transcription response. Supports arbitrarily long audio files with automatic intelligent chunking for longer audio.

        **Supported audio formats:** flac, m4a, mp3, mp4, mpeg, mpga, oga, ogg, wav, webm

        **Response format:** Returns JSON with transcribed text, duration, and language. Include `timestamp_granularities: ["word"]` to get word-level timestamps.

        **Pricing:** Batch transcription is priced at **1 credit per 2 seconds** of audio processed.

        <Note>
        For migrating from the OpenAI SDK, see our [OpenAI Whisper to Cartesia Ink Migration Guide](/api-reference/stt/migrate-from-open-ai).
        </Note>

        Parameters
        ----------
        file : core.File
            See core.File for more documentation

        model : str
            ID of the model to use for transcription. Use `ink-whisper` for the latest Cartesia Whisper model.

        encoding : typing.Optional[SttEncoding]
            The encoding format to process the audio as. If not specified, the audio file will be decoded automatically.

            **Supported formats:**
            - `pcm_s16le` - 16-bit signed integer PCM, little-endian (recommended for best performance)
            - `pcm_s32le` - 32-bit signed integer PCM, little-endian
            - `pcm_f16le` - 16-bit floating point PCM, little-endian
            - `pcm_f32le` - 32-bit floating point PCM, little-endian
            - `pcm_mulaw` - 8-bit μ-law encoded PCM
            - `pcm_alaw` - 8-bit A-law encoded PCM

        sample_rate : typing.Optional[int]
            The sample rate of the audio in Hz.

        language : typing.Optional[str]
            The language of the input audio in ISO-639-1 format. Defaults to `en`.

            <Accordion title="Supported languages">
              - `en` (English)
              - `zh` (Chinese)
              - `de` (German)
              - `es` (Spanish)
              - `ru` (Russian)
              - `ko` (Korean)
              - `fr` (French)
              - `ja` (Japanese)
              - `pt` (Portuguese)
              - `tr` (Turkish)
              - `pl` (Polish)
              - `ca` (Catalan)
              - `nl` (Dutch)
              - `ar` (Arabic)
              - `sv` (Swedish)
              - `it` (Italian)
              - `id` (Indonesian)
              - `hi` (Hindi)
              - `fi` (Finnish)
              - `vi` (Vietnamese)
              - `he` (Hebrew)
              - `uk` (Ukrainian)
              - `el` (Greek)
              - `ms` (Malay)
              - `cs` (Czech)
              - `ro` (Romanian)
              - `da` (Danish)
              - `hu` (Hungarian)
              - `ta` (Tamil)
              - `no` (Norwegian)
              - `th` (Thai)
              - `ur` (Urdu)
              - `hr` (Croatian)
              - `bg` (Bulgarian)
              - `lt` (Lithuanian)
              - `la` (Latin)
              - `mi` (Maori)
              - `ml` (Malayalam)
              - `cy` (Welsh)
              - `sk` (Slovak)
              - `te` (Telugu)
              - `fa` (Persian)
              - `lv` (Latvian)
              - `bn` (Bengali)
              - `sr` (Serbian)
              - `az` (Azerbaijani)
              - `sl` (Slovenian)
              - `kn` (Kannada)
              - `et` (Estonian)
              - `mk` (Macedonian)
              - `br` (Breton)
              - `eu` (Basque)
              - `is` (Icelandic)
              - `hy` (Armenian)
              - `ne` (Nepali)
              - `mn` (Mongolian)
              - `bs` (Bosnian)
              - `kk` (Kazakh)
              - `sq` (Albanian)
              - `sw` (Swahili)
              - `gl` (Galician)
              - `mr` (Marathi)
              - `pa` (Punjabi)
              - `si` (Sinhala)
              - `km` (Khmer)
              - `sn` (Shona)
              - `yo` (Yoruba)
              - `so` (Somali)
              - `af` (Afrikaans)
              - `oc` (Occitan)
              - `ka` (Georgian)
              - `be` (Belarusian)
              - `tg` (Tajik)
              - `sd` (Sindhi)
              - `gu` (Gujarati)
              - `am` (Amharic)
              - `yi` (Yiddish)
              - `lo` (Lao)
              - `uz` (Uzbek)
              - `fo` (Faroese)
              - `ht` (Haitian Creole)
              - `ps` (Pashto)
              - `tk` (Turkmen)
              - `nn` (Nynorsk)
              - `mt` (Maltese)
              - `sa` (Sanskrit)
              - `lb` (Luxembourgish)
              - `my` (Myanmar)
              - `bo` (Tibetan)
              - `tl` (Tagalog)
              - `mg` (Malagasy)
              - `as` (Assamese)
              - `tt` (Tatar)
              - `haw` (Hawaiian)
              - `ln` (Lingala)
              - `ha` (Hausa)
              - `ba` (Bashkir)
              - `jw` (Javanese)
              - `su` (Sundanese)
              - `yue` (Cantonese)
            </Accordion>

        timestamp_granularities : typing.Optional[typing.List[TimestampGranularity]]
            The timestamp granularities to populate for this transcription. Currently only `word` level timestamps are supported.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Returns
        -------
        TranscriptionResponse

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.stt.transcribe(
                model="ink-whisper",
                language="en",
            )


        asyncio.run(main())
        """
        _response = await self._client_wrapper.httpx_client.request(
            "stt",
            method="POST",
            params={
                "encoding": encoding,
                "sample_rate": sample_rate,
            },
            data={
                "model": model,
                "language": language,
                "timestamp_granularities[]": timestamp_granularities,
            },
            files={
                "file": file,
            },
            request_options=request_options,
            omit=OMIT,
        )
        try:
            if 200 <= _response.status_code < 300:
                return typing.cast(
                    TranscriptionResponse,
                    parse_obj_as(
                        type_=TranscriptionResponse,  # type: ignore
                        object_=_response.json(),
                    ),
                )
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
