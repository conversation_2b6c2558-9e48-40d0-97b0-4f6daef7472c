# This file was auto-generated by <PERSON>rn from our API Definition.

from __future__ import annotations
from ...core.pydantic_utilities import UniversalBaseModel
import typing
from ...tts.types.context_id import ContextId
from ...core.pydantic_utilities import IS_PYDANTIC_V2
import pydantic


class StreamingResponse_Chunk(UniversalBaseModel):
    type: typing.Literal["chunk"] = "chunk"
    data: str
    step_time: float
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class StreamingResponse_Done(UniversalBaseModel):
    type: typing.Literal["done"] = "done"
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


class StreamingResponse_Error(UniversalBaseModel):
    type: typing.Literal["error"] = "error"
    error: str
    context_id: typing.Optional[ContextId] = None
    status_code: int
    done: bool

    if IS_PYDANTIC_V2:
        model_config: typing.ClassVar[pydantic.ConfigDict] = pydantic.ConfigDict(extra="allow", frozen=True)  # type: ignore # Pydantic v2
    else:

        class Config:
            frozen = True
            smart_union = True
            extra = pydantic.Extra.allow


StreamingResponse = typing.Union[StreamingResponse_Chunk, StreamingResponse_Done, StreamingResponse_Error]
