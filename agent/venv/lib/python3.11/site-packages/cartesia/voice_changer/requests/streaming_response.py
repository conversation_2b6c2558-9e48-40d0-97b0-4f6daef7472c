# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations
import typing_extensions
import typing
import typing_extensions
from ...tts.types.context_id import ContextId


class StreamingResponse_ChunkParams(typing_extensions.TypedDict):
    type: typing.Literal["chunk"]
    data: str
    step_time: float
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


class StreamingResponse_DoneParams(typing_extensions.TypedDict):
    type: typing.Literal["done"]
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


class StreamingResponse_ErrorParams(typing_extensions.TypedDict):
    type: typing.Literal["error"]
    error: str
    context_id: typing_extensions.NotRequired[ContextId]
    status_code: int
    done: bool


StreamingResponseParams = typing.Union[
    StreamingResponse_ChunkParams, StreamingResponse_DoneParams, StreamingResponse_ErrorParams
]
