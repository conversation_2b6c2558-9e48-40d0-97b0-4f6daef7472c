# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
from ..core.client_wrapper import SyncC<PERSON><PERSON>rapper
from .. import core
from .types.output_format_container import OutputFormatContainer
from ..tts.types.raw_encoding import RawEncoding
from ..core.request_options import RequestOptions
from json.decoder import J<PERSON><PERSON><PERSON>ode<PERSON>rror
from ..core.api_error import Api<PERSON>rror
from .types.streaming_response import StreamingResponse
from ..core.pydantic_utilities import parse_obj_as
import json
from ..core.client_wrapper import AsyncClientWrapper

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class VoiceChangerClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def bytes(
        self,
        *,
        clip: core.File,
        voice_id: str,
        output_format_container: OutputFormatContainer,
        output_format_sample_rate: int,
        output_format_encoding: typing.Optional[RawEncoding] = OMIT,
        output_format_bit_rate: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[bytes]:
        """
        Takes an audio file of speech, and returns an audio file of speech spoken with the same intonation, but with a different voice.

        This endpoint is priced at 15 characters per second of input audio.

        Parameters
        ----------
        clip : core.File
            See core.File for more documentation

        voice_id : str

        output_format_container : OutputFormatContainer

        output_format_sample_rate : int

        output_format_encoding : typing.Optional[RawEncoding]
            Required for `raw` and `wav` containers.

        output_format_bit_rate : typing.Optional[int]
            Required for `mp3` containers.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.Iterator[bytes]

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        client.voice_changer.bytes(
            voice_id="694f9389-aac1-45b6-b726-9d9369183238",
            output_format_container="mp3",
            output_format_sample_rate=44100,
            output_format_bit_rate=128000,
        )
        """
        with self._client_wrapper.httpx_client.stream(
            "voice-changer/bytes",
            method="POST",
            data={
                "voice[id]": voice_id,
                "output_format[container]": output_format_container,
                "output_format[sample_rate]": output_format_sample_rate,
                "output_format[encoding]": output_format_encoding,
                "output_format[bit_rate]": output_format_bit_rate,
            },
            files={
                "clip": clip,
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", None) if request_options is not None else None
                    for _chunk in _response.iter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                _response.read()
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    def sse(
        self,
        *,
        clip: core.File,
        voice_id: str,
        output_format_container: OutputFormatContainer,
        output_format_sample_rate: int,
        output_format_encoding: typing.Optional[RawEncoding] = OMIT,
        output_format_bit_rate: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.Iterator[StreamingResponse]:
        """
        Parameters
        ----------
        clip : core.File
            See core.File for more documentation

        voice_id : str

        output_format_container : OutputFormatContainer

        output_format_sample_rate : int

        output_format_encoding : typing.Optional[RawEncoding]
            Required for `raw` and `wav` containers.

        output_format_bit_rate : typing.Optional[int]
            Required for `mp3` containers.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.Iterator[StreamingResponse]

        Examples
        --------
        from cartesia import Cartesia

        client = Cartesia(
            api_key="YOUR_API_KEY",
        )
        response = client.voice_changer.sse(
            voice_id="694f9389-aac1-45b6-b726-9d9369183238",
            output_format_container="mp3",
            output_format_sample_rate=44100,
            output_format_bit_rate=128000,
        )
        for chunk in response:
            yield chunk
        """
        with self._client_wrapper.httpx_client.stream(
            "voice-changer/sse",
            method="POST",
            data={
                "voice[id]": voice_id,
                "output_format[container]": output_format_container,
                "output_format[sample_rate]": output_format_sample_rate,
                "output_format[encoding]": output_format_encoding,
                "output_format[bit_rate]": output_format_bit_rate,
            },
            files={
                "clip": clip,
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    for _text in _response.iter_lines():
                        try:
                            if len(_text) == 0:
                                continue
                            yield typing.cast(
                                StreamingResponse,
                                parse_obj_as(
                                    type_=StreamingResponse,  # type: ignore
                                    object_=json.loads(_text),
                                ),
                            )
                        except:
                            pass
                    return
                _response.read()
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncVoiceChangerClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def bytes(
        self,
        *,
        clip: core.File,
        voice_id: str,
        output_format_container: OutputFormatContainer,
        output_format_sample_rate: int,
        output_format_encoding: typing.Optional[RawEncoding] = OMIT,
        output_format_bit_rate: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[bytes]:
        """
        Takes an audio file of speech, and returns an audio file of speech spoken with the same intonation, but with a different voice.

        This endpoint is priced at 15 characters per second of input audio.

        Parameters
        ----------
        clip : core.File
            See core.File for more documentation

        voice_id : str

        output_format_container : OutputFormatContainer

        output_format_sample_rate : int

        output_format_encoding : typing.Optional[RawEncoding]
            Required for `raw` and `wav` containers.

        output_format_bit_rate : typing.Optional[int]
            Required for `mp3` containers.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration. You can pass in configuration such as `chunk_size`, and more to customize the request and response.

        Yields
        ------
        typing.AsyncIterator[bytes]

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            await client.voice_changer.bytes(
                voice_id="694f9389-aac1-45b6-b726-9d9369183238",
                output_format_container="mp3",
                output_format_sample_rate=44100,
                output_format_bit_rate=128000,
            )


        asyncio.run(main())
        """
        async with self._client_wrapper.httpx_client.stream(
            "voice-changer/bytes",
            method="POST",
            data={
                "voice[id]": voice_id,
                "output_format[container]": output_format_container,
                "output_format[sample_rate]": output_format_sample_rate,
                "output_format[encoding]": output_format_encoding,
                "output_format[bit_rate]": output_format_bit_rate,
            },
            files={
                "clip": clip,
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    _chunk_size = request_options.get("chunk_size", None) if request_options is not None else None
                    async for _chunk in _response.aiter_bytes(chunk_size=_chunk_size):
                        yield _chunk
                    return
                await _response.aread()
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)

    async def sse(
        self,
        *,
        clip: core.File,
        voice_id: str,
        output_format_container: OutputFormatContainer,
        output_format_sample_rate: int,
        output_format_encoding: typing.Optional[RawEncoding] = OMIT,
        output_format_bit_rate: typing.Optional[int] = OMIT,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> typing.AsyncIterator[StreamingResponse]:
        """
        Parameters
        ----------
        clip : core.File
            See core.File for more documentation

        voice_id : str

        output_format_container : OutputFormatContainer

        output_format_sample_rate : int

        output_format_encoding : typing.Optional[RawEncoding]
            Required for `raw` and `wav` containers.

        output_format_bit_rate : typing.Optional[int]
            Required for `mp3` containers.

        request_options : typing.Optional[RequestOptions]
            Request-specific configuration.

        Yields
        ------
        typing.AsyncIterator[StreamingResponse]

        Examples
        --------
        import asyncio

        from cartesia import AsyncCartesia

        client = AsyncCartesia(
            api_key="YOUR_API_KEY",
        )


        async def main() -> None:
            response = await client.voice_changer.sse(
                voice_id="694f9389-aac1-45b6-b726-9d9369183238",
                output_format_container="mp3",
                output_format_sample_rate=44100,
                output_format_bit_rate=128000,
            )
            async for chunk in response:
                yield chunk


        asyncio.run(main())
        """
        async with self._client_wrapper.httpx_client.stream(
            "voice-changer/sse",
            method="POST",
            data={
                "voice[id]": voice_id,
                "output_format[container]": output_format_container,
                "output_format[sample_rate]": output_format_sample_rate,
                "output_format[encoding]": output_format_encoding,
                "output_format[bit_rate]": output_format_bit_rate,
            },
            files={
                "clip": clip,
            },
            request_options=request_options,
            omit=OMIT,
        ) as _response:
            try:
                if 200 <= _response.status_code < 300:
                    async for _text in _response.aiter_lines():
                        try:
                            if len(_text) == 0:
                                continue
                            yield typing.cast(
                                StreamingResponse,
                                parse_obj_as(
                                    type_=StreamingResponse,  # type: ignore
                                    object_=json.loads(_text),
                                ),
                            )
                        except:
                            pass
                    return
                await _response.aread()
                _response_json = _response.json()
            except JSONDecodeError:
                raise ApiError(status_code=_response.status_code, body=_response.text)
            raise ApiError(status_code=_response.status_code, body=_response_json)
