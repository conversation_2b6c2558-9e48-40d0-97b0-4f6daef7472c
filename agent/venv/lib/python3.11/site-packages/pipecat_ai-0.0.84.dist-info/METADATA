Metadata-Version: 2.4
Name: pipecat-ai
Version: 0.0.84
Summary: An open source framework for voice (and multimodal) assistants
License-Expression: BSD-2-Clause
Project-URL: Source, https://github.com/pipecat-ai/pipecat
Project-URL: Website, https://pipecat.ai
Keywords: webrtc,audio,video,ai
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: Communications :: Conferencing
Classifier: Topic :: Multimedia :: Sound/Audio
Classifier: Topic :: Multimedia :: Video
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.10
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: aiofiles<25,>=24.1.0
Requires-Dist: aiohttp<4,>=3.11.12
Requires-Dist: audioop-lts~=0.2.1; python_version >= "3.13"
Requires-Dist: docstring_parser~=0.16
Requires-Dist: loguru~=0.7.3
Requires-Dist: Markdown<4,>=3.7
Requires-Dist: nltk<4,>=3.9.1
Requires-Dist: numpy<3,>=1.26.4
Requires-Dist: Pillow<12,>=11.1.0
Requires-Dist: protobuf~=5.29.3
Requires-Dist: pydantic<3,>=2.10.6
Requires-Dist: pyloudnorm~=0.1.1
Requires-Dist: resampy~=0.4.3
Requires-Dist: soxr~=0.5.0
Requires-Dist: openai<=1.99.1,>=1.74.0
Requires-Dist: numba==0.61.2
Requires-Dist: wait_for2>=0.4.1; python_version < "3.12"
Provides-Extra: aic
Requires-Dist: aic-sdk~=1.0.1; extra == "aic"
Provides-Extra: anthropic
Requires-Dist: anthropic~=0.49.0; extra == "anthropic"
Provides-Extra: assemblyai
Requires-Dist: websockets<15.0,>=13.1; extra == "assemblyai"
Provides-Extra: asyncai
Requires-Dist: websockets<15.0,>=13.1; extra == "asyncai"
Provides-Extra: aws
Requires-Dist: aioboto3~=15.0.0; extra == "aws"
Requires-Dist: websockets<15.0,>=13.1; extra == "aws"
Provides-Extra: aws-nova-sonic
Requires-Dist: aws_sdk_bedrock_runtime~=0.0.2; python_version >= "3.12" and extra == "aws-nova-sonic"
Provides-Extra: azure
Requires-Dist: azure-cognitiveservices-speech~=1.42.0; extra == "azure"
Provides-Extra: cartesia
Requires-Dist: cartesia~=2.0.3; extra == "cartesia"
Requires-Dist: websockets<15.0,>=13.1; extra == "cartesia"
Provides-Extra: cerebras
Provides-Extra: deepseek
Provides-Extra: daily
Requires-Dist: daily-python~=0.19.9; extra == "daily"
Provides-Extra: deepgram
Requires-Dist: deepgram-sdk~=4.7.0; extra == "deepgram"
Provides-Extra: elevenlabs
Requires-Dist: websockets<15.0,>=13.1; extra == "elevenlabs"
Provides-Extra: fal
Requires-Dist: fal-client~=0.5.9; extra == "fal"
Provides-Extra: fireworks
Provides-Extra: fish
Requires-Dist: ormsgpack~=1.7.0; extra == "fish"
Requires-Dist: websockets<15.0,>=13.1; extra == "fish"
Provides-Extra: gladia
Requires-Dist: websockets<15.0,>=13.1; extra == "gladia"
Provides-Extra: google
Requires-Dist: google-cloud-speech~=2.32.0; extra == "google"
Requires-Dist: google-cloud-texttospeech~=2.26.0; extra == "google"
Requires-Dist: google-genai~=1.24.0; extra == "google"
Requires-Dist: websockets<15.0,>=13.1; extra == "google"
Provides-Extra: grok
Provides-Extra: groq
Requires-Dist: groq~=0.23.0; extra == "groq"
Provides-Extra: gstreamer
Requires-Dist: pygobject~=3.50.0; extra == "gstreamer"
Provides-Extra: heygen
Requires-Dist: livekit>=0.22.0; extra == "heygen"
Requires-Dist: websockets<15.0,>=13.1; extra == "heygen"
Provides-Extra: inworld
Provides-Extra: krisp
Requires-Dist: pipecat-ai-krisp~=0.4.0; extra == "krisp"
Provides-Extra: koala
Requires-Dist: pvkoala~=2.0.3; extra == "koala"
Provides-Extra: langchain
Requires-Dist: langchain~=0.3.20; extra == "langchain"
Requires-Dist: langchain-community~=0.3.20; extra == "langchain"
Requires-Dist: langchain-openai~=0.3.9; extra == "langchain"
Provides-Extra: livekit
Requires-Dist: livekit~=0.22.0; extra == "livekit"
Requires-Dist: livekit-api~=0.8.2; extra == "livekit"
Requires-Dist: tenacity<10.0.0,>=8.2.3; extra == "livekit"
Provides-Extra: lmnt
Requires-Dist: websockets<15.0,>=13.1; extra == "lmnt"
Provides-Extra: local
Requires-Dist: pyaudio~=0.2.14; extra == "local"
Provides-Extra: mcp
Requires-Dist: mcp[cli]~=1.9.4; extra == "mcp"
Provides-Extra: mem0
Requires-Dist: mem0ai~=0.1.94; extra == "mem0"
Provides-Extra: mistral
Provides-Extra: mlx-whisper
Requires-Dist: mlx-whisper~=0.4.2; extra == "mlx-whisper"
Provides-Extra: moondream
Requires-Dist: accelerate~=1.10.0; extra == "moondream"
Requires-Dist: einops~=0.8.0; extra == "moondream"
Requires-Dist: pyvips[binary]~=3.0.0; extra == "moondream"
Requires-Dist: timm~=1.0.13; extra == "moondream"
Requires-Dist: transformers>=4.48.0; extra == "moondream"
Provides-Extra: nim
Provides-Extra: neuphonic
Requires-Dist: websockets<15.0,>=13.1; extra == "neuphonic"
Provides-Extra: noisereduce
Requires-Dist: noisereduce~=3.0.3; extra == "noisereduce"
Provides-Extra: openai
Requires-Dist: websockets<15.0,>=13.1; extra == "openai"
Provides-Extra: openpipe
Requires-Dist: openpipe~=4.50.0; extra == "openpipe"
Provides-Extra: openrouter
Provides-Extra: perplexity
Provides-Extra: playht
Requires-Dist: websockets<15.0,>=13.1; extra == "playht"
Provides-Extra: qwen
Provides-Extra: rime
Requires-Dist: websockets<15.0,>=13.1; extra == "rime"
Provides-Extra: riva
Requires-Dist: nvidia-riva-client~=2.21.1; extra == "riva"
Provides-Extra: runner
Requires-Dist: python-dotenv<2.0.0,>=1.0.0; extra == "runner"
Requires-Dist: uvicorn<1.0.0,>=0.32.0; extra == "runner"
Requires-Dist: fastapi<0.117.0,>=0.115.6; extra == "runner"
Requires-Dist: pipecat-ai-small-webrtc-prebuilt>=1.0.0; extra == "runner"
Provides-Extra: sambanova
Provides-Extra: sarvam
Requires-Dist: websockets<15.0,>=13.1; extra == "sarvam"
Provides-Extra: sentry
Requires-Dist: sentry-sdk~=2.23.1; extra == "sentry"
Provides-Extra: local-smart-turn
Requires-Dist: coremltools>=8.0; extra == "local-smart-turn"
Requires-Dist: transformers; extra == "local-smart-turn"
Requires-Dist: torch<3,>=2.5.0; extra == "local-smart-turn"
Requires-Dist: torchaudio<3,>=2.5.0; extra == "local-smart-turn"
Provides-Extra: remote-smart-turn
Provides-Extra: silero
Requires-Dist: onnxruntime~=1.20.1; extra == "silero"
Provides-Extra: simli
Requires-Dist: simli-ai~=0.1.10; extra == "simli"
Provides-Extra: soniox
Requires-Dist: websockets<15.0,>=13.1; extra == "soniox"
Provides-Extra: soundfile
Requires-Dist: soundfile~=0.13.0; extra == "soundfile"
Provides-Extra: speechmatics
Requires-Dist: speechmatics-rt>=0.4.0; extra == "speechmatics"
Provides-Extra: tavus
Provides-Extra: together
Provides-Extra: tracing
Requires-Dist: opentelemetry-sdk>=1.33.0; extra == "tracing"
Requires-Dist: opentelemetry-api>=1.33.0; extra == "tracing"
Requires-Dist: opentelemetry-instrumentation>=0.54b0; extra == "tracing"
Provides-Extra: ultravox
Requires-Dist: transformers>=4.48.0; extra == "ultravox"
Requires-Dist: vllm>=0.9.0; extra == "ultravox"
Provides-Extra: webrtc
Requires-Dist: aiortc~=1.11.0; extra == "webrtc"
Requires-Dist: opencv-python~=*********; extra == "webrtc"
Provides-Extra: websocket
Requires-Dist: websockets<15.0,>=13.1; extra == "websocket"
Requires-Dist: fastapi<0.117.0,>=0.115.6; extra == "websocket"
Provides-Extra: whisper
Requires-Dist: faster-whisper~=1.1.1; extra == "whisper"
Dynamic: license-file

<h1><div align="center">
 <img alt="pipecat" width="300px" height="auto" src="https://raw.githubusercontent.com/pipecat-ai/pipecat/main/pipecat.png">
</div></h1>

[![PyPI](https://img.shields.io/pypi/v/pipecat-ai)](https://pypi.org/project/pipecat-ai) ![Tests](https://github.com/pipecat-ai/pipecat/actions/workflows/tests.yaml/badge.svg) [![codecov](https://codecov.io/gh/pipecat-ai/pipecat/graph/badge.svg?token=LNVUIVO4Y9)](https://codecov.io/gh/pipecat-ai/pipecat) [![Docs](https://img.shields.io/badge/Documentation-blue)](https://docs.pipecat.ai) [![Discord](https://img.shields.io/discord/1239284677165056021)](https://discord.gg/pipecat)

# 🎙️ Pipecat: Real-Time Voice & Multimodal AI Agents

**Pipecat** is an open-source Python framework for building real-time voice and multimodal conversational agents. Orchestrate audio and video, AI services, different transports, and conversation pipelines effortlessly—so you can focus on what makes your agent unique.

> Want to dive right in? Try the [quickstart](https://docs.pipecat.ai/getting-started/quickstart).

## 🚀 What You Can Build

- **Voice Assistants** – natural, streaming conversations with AI
- **AI Companions** – coaches, meeting assistants, characters
- **Multimodal Interfaces** – voice, video, images, and more
- **Interactive Storytelling** – creative tools with generative media
- **Business Agents** – customer intake, support bots, guided flows
- **Complex Dialog Systems** – design logic with structured conversations

🧭 Looking to build structured conversations? Check out [Pipecat Flows](https://github.com/pipecat-ai/pipecat-flows) for managing complex conversational states and transitions.

## 🧠 Why Pipecat?

- **Voice-first**: Integrates speech recognition, text-to-speech, and conversation handling
- **Pluggable**: Supports many AI services and tools
- **Composable Pipelines**: Build complex behavior from modular components
- **Real-Time**: Ultra-low latency interaction with different transports (e.g. WebSockets or WebRTC)

## 🎬 See it in action

<p float="left">
    <a href="https://github.com/pipecat-ai/pipecat-examples/tree/main/simple-chatbot"><img src="https://raw.githubusercontent.com/pipecat-ai/pipecat-examples/main/simple-chatbot/image.png" width="400" /></a>&nbsp;
    <a href="https://github.com/pipecat-ai/pipecat-examples/tree/main/storytelling-chatbot"><img src="https://raw.githubusercontent.com/pipecat-ai/pipecat-examples/main/storytelling-chatbot/image.png" width="400" /></a>
    <br/>
    <a href="https://github.com/pipecat-ai/pipecat-examples/tree/main/translation-chatbot"><img src="https://raw.githubusercontent.com/pipecat-ai/pipecat-examples/main/translation-chatbot/image.png" width="400" /></a>&nbsp;
    <a href="https://github.com/pipecat-ai/pipecat-examples/tree/main/moondream-chatbot"><img src="https://raw.githubusercontent.com/pipecat-ai/pipecat-examples/main/moondream-chatbot/image.png" width="400" /></a>
</p>

## 📱 Client SDKs

You can connect to Pipecat from any platform using our official SDKs:

| Platform | SDK Repo                                                                       | Description                      |
| -------- | ------------------------------------------------------------------------------ | -------------------------------- |
| Web      | [pipecat-client-web](https://github.com/pipecat-ai/pipecat-client-web)         | JavaScript and React client SDKs |
| iOS      | [pipecat-client-ios](https://github.com/pipecat-ai/pipecat-client-ios)         | Swift SDK for iOS                |
| Android  | [pipecat-client-android](https://github.com/pipecat-ai/pipecat-client-android) | Kotlin SDK for Android           |
| C++      | [pipecat-client-cxx](https://github.com/pipecat-ai/pipecat-client-cxx)         | C++ client SDK                   |

## 🧩 Available services

| Category            | Services                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| ------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Speech-to-Text      | [AssemblyAI](https://docs.pipecat.ai/server/services/stt/assemblyai), [AWS](https://docs.pipecat.ai/server/services/stt/aws), [Azure](https://docs.pipecat.ai/server/services/stt/azure), [Cartesia](https://docs.pipecat.ai/server/services/stt/cartesia), [Deepgram](https://docs.pipecat.ai/server/services/stt/deepgram), [Fal Wizper](https://docs.pipecat.ai/server/services/stt/fal), [Gladia](https://docs.pipecat.ai/server/services/stt/gladia), [Google](https://docs.pipecat.ai/server/services/stt/google), [Groq (Whisper)](https://docs.pipecat.ai/server/services/stt/groq), [NVIDIA Riva](https://docs.pipecat.ai/server/services/stt/riva), [OpenAI (Whisper)](https://docs.pipecat.ai/server/services/stt/openai), [SambaNova (Whisper)](https://docs.pipecat.ai/server/services/stt/sambanova), [Soniox](https://docs.pipecat.ai/server/services/stt/soniox), [Speechmatics](https://docs.pipecat.ai/server/services/stt/speechmatics), [Ultravox](https://docs.pipecat.ai/server/services/stt/ultravox), [Whisper](https://docs.pipecat.ai/server/services/stt/whisper)                                                                                                                                                                                          |
| LLMs                | [Anthropic](https://docs.pipecat.ai/server/services/llm/anthropic), [AWS](https://docs.pipecat.ai/server/services/llm/aws), [Azure](https://docs.pipecat.ai/server/services/llm/azure), [Cerebras](https://docs.pipecat.ai/server/services/llm/cerebras), [DeepSeek](https://docs.pipecat.ai/server/services/llm/deepseek), [Fireworks AI](https://docs.pipecat.ai/server/services/llm/fireworks), [Gemini](https://docs.pipecat.ai/server/services/llm/gemini), [Grok](https://docs.pipecat.ai/server/services/llm/grok), [Groq](https://docs.pipecat.ai/server/services/llm/groq), [Mistral](https://docs.pipecat.ai/server/services/llm/mistral), [NVIDIA NIM](https://docs.pipecat.ai/server/services/llm/nim), [Ollama](https://docs.pipecat.ai/server/services/llm/ollama), [OpenAI](https://docs.pipecat.ai/server/services/llm/openai), [OpenRouter](https://docs.pipecat.ai/server/services/llm/openrouter), [Perplexity](https://docs.pipecat.ai/server/services/llm/perplexity), [Qwen](https://docs.pipecat.ai/server/services/llm/qwen), [SambaNova](https://docs.pipecat.ai/server/services/llm/sambanova) [Together AI](https://docs.pipecat.ai/server/services/llm/together)                                                                                          |
| Text-to-Speech      | [Async](https://docs.pipecat.ai/server/services/tts/asyncai), [AWS](https://docs.pipecat.ai/server/services/tts/aws), [Azure](https://docs.pipecat.ai/server/services/tts/azure), [Cartesia](https://docs.pipecat.ai/server/services/tts/cartesia), [Deepgram](https://docs.pipecat.ai/server/services/tts/deepgram), [ElevenLabs](https://docs.pipecat.ai/server/services/tts/elevenlabs), [Fish](https://docs.pipecat.ai/server/services/tts/fish), [Google](https://docs.pipecat.ai/server/services/tts/google), [Groq](https://docs.pipecat.ai/server/services/tts/groq), [Inworld](https://docs.pipecat.ai/server/services/tts/inworld), [LMNT](https://docs.pipecat.ai/server/services/tts/lmnt), [MiniMax](https://docs.pipecat.ai/server/services/tts/minimax), [Neuphonic](https://docs.pipecat.ai/server/services/tts/neuphonic), [NVIDIA Riva](https://docs.pipecat.ai/server/services/tts/riva), [OpenAI](https://docs.pipecat.ai/server/services/tts/openai), [Piper](https://docs.pipecat.ai/server/services/tts/piper), [PlayHT](https://docs.pipecat.ai/server/services/tts/playht), [Rime](https://docs.pipecat.ai/server/services/tts/rime), [Sarvam](https://docs.pipecat.ai/server/services/tts/sarvam), [XTTS](https://docs.pipecat.ai/server/services/tts/xtts) |
| Speech-to-Speech    | [AWS Nova Sonic](https://docs.pipecat.ai/server/services/s2s/aws), [Gemini Multimodal Live](https://docs.pipecat.ai/server/services/s2s/gemini), [OpenAI Realtime](https://docs.pipecat.ai/server/services/s2s/openai)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Transport           | [Daily (WebRTC)](https://docs.pipecat.ai/server/services/transport/daily), [FastAPI Websocket](https://docs.pipecat.ai/server/services/transport/fastapi-websocket), [SmallWebRTCTransport](https://docs.pipecat.ai/server/services/transport/small-webrtc), [WebSocket Server](https://docs.pipecat.ai/server/services/transport/websocket-server), Local                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Serializers         | [Plivo](https://docs.pipecat.ai/server/utilities/serializers/plivo), [Twilio](https://docs.pipecat.ai/server/utilities/serializers/twilio), [Telnyx](https://docs.pipecat.ai/server/utilities/serializers/telnyx)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| Video               | [HeyGen](https://docs.pipecat.ai/server/services/video/heygen), [Tavus](https://docs.pipecat.ai/server/services/video/tavus), [Simli](https://docs.pipecat.ai/server/services/video/simli)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| Memory              | [mem0](https://docs.pipecat.ai/server/services/memory/mem0)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Vision & Image      | [fal](https://docs.pipecat.ai/server/services/image-generation/fal), [Google Imagen](https://docs.pipecat.ai/server/services/image-generation/fal), [Moondream](https://docs.pipecat.ai/server/services/vision/moondream)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| Audio Processing    | [Silero VAD](https://docs.pipecat.ai/server/utilities/audio/silero-vad-analyzer), [Krisp](https://docs.pipecat.ai/server/utilities/audio/krisp-filter), [Koala](https://docs.pipecat.ai/server/utilities/audio/koala-filter), [ai-coustics](https://docs.pipecat.ai/server/utilities/audio/aic-filter), [Noisereduce](https://docs.pipecat.ai/server/utilities/audio/noisereduce-filter)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Analytics & Metrics | [OpenTelemetry](https://docs.pipecat.ai/server/utilities/opentelemetry), [Sentry](https://docs.pipecat.ai/server/services/analytics/sentry)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

📚 [View full services documentation →](https://docs.pipecat.ai/server/services/supported-services)

## ⚡ Getting started

You can get started with Pipecat running on your local machine, then move your agent processes to the cloud when you're ready.

1. Install uv

   ```bash
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

   > **Need help?** Refer to the [uv install documentation](https://docs.astral.sh/uv/getting-started/installation/).

2. Install the module

   ```bash
   # For new projects
   uv init my-pipecat-app
   cd my-pipecat-app
   uv add pipecat-ai

   # Or for existing projects
   uv add pipecat-ai
   ```

3. Set up your environment

   ```bash
   cp env.example .env
   ```

4. To keep things lightweight, only the core framework is included by default. If you need support for third-party AI services, you can add the necessary dependencies with:

   ```bash
   uv add "pipecat-ai[option,...]"
   ```

> **Using pip?** You can still use `pip install pipecat-ai` and `pip install "pipecat-ai[option,...]"` to get set up.

## 🧪 Code examples

- [Foundational](https://github.com/pipecat-ai/pipecat/tree/main/examples/foundational) — small snippets that build on each other, introducing one or two concepts at a time
- [Example apps](https://github.com/pipecat-ai/pipecat-examples) — complete applications that you can use as starting points for development

## 🛠️ Contributing to the framework

### Prerequisites

**Minimum Python Version:** 3.10
**Recommended Python Version:** 3.12

### Setup Steps

1. Clone the repository and navigate to it:

   ```bash
   git clone https://github.com/pipecat-ai/pipecat.git
   cd pipecat
   ```

2. Install development and testing dependencies:

   ```bash
   uv sync --group dev --all-extras --no-extra gstreamer --no-extra krisp --no-extra local
   ```

3. Install the git pre-commit hooks:

   ```bash
   uv run pre-commit install
   ```

### Python 3.13+ Compatibility

Some features require PyTorch, which doesn't yet support Python 3.13+. Install using:

```bash
uv sync --group dev --all-extras \
  --no-extra gstreamer \
  --no-extra krisp \
  --no-extra local \
  --no-extra local-smart-turn \
  --no-extra mlx-whisper \
  --no-extra moondream \
  --no-extra ultravox
```

> **Tip:** For full compatibility, use Python 3.12: `uv python pin 3.12`

> **Note**: Some extras (local, gstreamer) require system dependencies. See documentation if you encounter build errors.

### Running tests

To run all tests, from the root directory:

```bash
uv run pytest
```

Run a specific test suite:

```bash
uv run pytest tests/test_name.py
```

### Setting up your editor

This project uses strict [PEP 8](https://peps.python.org/pep-0008/) formatting via [Ruff](https://github.com/astral-sh/ruff).

#### Emacs

You can use [use-package](https://github.com/jwiegley/use-package) to install [emacs-lazy-ruff](https://github.com/christophermadsen/emacs-lazy-ruff) package and configure `ruff` arguments:

```elisp
(use-package lazy-ruff
  :ensure t
  :hook ((python-mode . lazy-ruff-mode))
  :config
  (setq lazy-ruff-format-command "ruff format")
  (setq lazy-ruff-check-command "ruff check --select I"))
```

`ruff` was installed in the `venv` environment described before, so you should be able to use [pyvenv-auto](https://github.com/ryotaro612/pyvenv-auto) to automatically load that environment inside Emacs.

```elisp
(use-package pyvenv-auto
  :ensure t
  :defer t
  :hook ((python-mode . pyvenv-auto-run)))
```

#### Visual Studio Code

Install the
[Ruff](https://marketplace.visualstudio.com/items?itemName=charliermarsh.ruff) extension. Then edit the user settings (_Ctrl-Shift-P_ `Open User Settings (JSON)`) and set it as the default Python formatter, and enable formatting on save:

```json
"[python]": {
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.formatOnSave": true
}
```

#### PyCharm

`ruff` was installed in the `venv` environment described before, now to enable autoformatting on save, go to `File` -> `Settings` -> `Tools` -> `File Watchers` and add a new watcher with the following settings:

1. **Name**: `Ruff formatter`
2. **File type**: `Python`
3. **Working directory**: `$ContentRoot$`
4. **Arguments**: `format $FilePath$`
5. **Program**: `$PyInterpreterDirectory$/ruff`

## 🤝 Contributing

We welcome contributions from the community! Whether you're fixing bugs, improving documentation, or adding new features, here's how you can help:

- **Found a bug?** Open an [issue](https://github.com/pipecat-ai/pipecat/issues)
- **Have a feature idea?** Start a [discussion](https://discord.gg/pipecat)
- **Want to contribute code?** Check our [CONTRIBUTING.md](CONTRIBUTING.md) guide
- **Documentation improvements?** [Docs](https://github.com/pipecat-ai/docs) PRs are always welcome

Before submitting a pull request, please check existing issues and PRs to avoid duplicates.

We aim to review all contributions promptly and provide constructive feedback to help get your changes merged.

## 🛟 Getting help

➡️ [Join our Discord](https://discord.gg/pipecat)

➡️ [Read the docs](https://docs.pipecat.ai)

➡️ [Reach us on X](https://x.com/pipecat_ai)
