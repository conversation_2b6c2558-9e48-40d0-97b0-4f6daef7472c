# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: data_stream.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from . import handle_pb2 as handle__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x64\x61ta_stream.proto\x12\rlivekit.proto\x1a\x0chandle.proto\"s\n\x15OwnedTextStreamReader\x12-\n\x06handle\x18\x01 \x02(\x0b\x32\x1d.livekit.proto.FfiOwnedHandle\x12+\n\x04info\x18\x02 \x02(\x0b\x32\x1d.livekit.proto.TextStreamInfo\"?\n&TextStreamReaderReadIncrementalRequest\x12\x15\n\rreader_handle\x18\x01 \x02(\x04\")\n\'TextStreamReaderReadIncrementalResponse\"7\n\x1eTextStreamReaderReadAllRequest\x12\x15\n\rreader_handle\x18\x01 \x02(\x04\"3\n\x1fTextStreamReaderReadAllResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"}\n\x1fTextStreamReaderReadAllCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x11\n\x07\x63ontent\x18\x02 \x01(\tH\x00\x12+\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.StreamErrorH\x00\x42\x08\n\x06result\"\xb3\x01\n\x15TextStreamReaderEvent\x12\x15\n\rreader_handle\x18\x01 \x02(\x04\x12\x46\n\x0e\x63hunk_received\x18\x02 \x01(\x0b\x32,.livekit.proto.TextStreamReaderChunkReceivedH\x00\x12\x31\n\x03\x65os\x18\x03 \x01(\x0b\x32\".livekit.proto.TextStreamReaderEOSH\x00\x42\x08\n\x06\x64\x65tail\"0\n\x1dTextStreamReaderChunkReceived\x12\x0f\n\x07\x63ontent\x18\x01 \x02(\t\"@\n\x13TextStreamReaderEOS\x12)\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x1a.livekit.proto.StreamError\"s\n\x15OwnedByteStreamReader\x12-\n\x06handle\x18\x01 \x02(\x0b\x32\x1d.livekit.proto.FfiOwnedHandle\x12+\n\x04info\x18\x02 \x02(\x0b\x32\x1d.livekit.proto.ByteStreamInfo\"?\n&ByteStreamReaderReadIncrementalRequest\x12\x15\n\rreader_handle\x18\x01 \x02(\x04\")\n\'ByteStreamReaderReadIncrementalResponse\"7\n\x1e\x42yteStreamReaderReadAllRequest\x12\x15\n\rreader_handle\x18\x01 \x02(\x04\"3\n\x1f\x42yteStreamReaderReadAllResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"}\n\x1f\x42yteStreamReaderReadAllCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x11\n\x07\x63ontent\x18\x02 \x01(\x0cH\x00\x12+\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.StreamErrorH\x00\x42\x08\n\x06result\"e\n\"ByteStreamReaderWriteToFileRequest\x12\x15\n\rreader_handle\x18\x01 \x02(\x04\x12\x11\n\tdirectory\x18\x03 \x01(\t\x12\x15\n\rname_override\x18\x04 \x01(\t\"7\n#ByteStreamReaderWriteToFileResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\x83\x01\n#ByteStreamReaderWriteToFileCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x13\n\tfile_path\x18\x02 \x01(\tH\x00\x12+\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.StreamErrorH\x00\x42\x08\n\x06result\"\xb3\x01\n\x15\x42yteStreamReaderEvent\x12\x15\n\rreader_handle\x18\x01 \x02(\x04\x12\x46\n\x0e\x63hunk_received\x18\x02 \x01(\x0b\x32,.livekit.proto.ByteStreamReaderChunkReceivedH\x00\x12\x31\n\x03\x65os\x18\x03 \x01(\x0b\x32\".livekit.proto.ByteStreamReaderEOSH\x00\x42\x08\n\x06\x64\x65tail\"0\n\x1d\x42yteStreamReaderChunkReceived\x12\x0f\n\x07\x63ontent\x18\x01 \x02(\x0c\"@\n\x13\x42yteStreamReaderEOS\x12)\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x1a.livekit.proto.StreamError\"\x7f\n\x15StreamSendFileRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x31\n\x07options\x18\x02 \x02(\x0b\x32 .livekit.proto.StreamByteOptions\x12\x11\n\tfile_path\x18\x03 \x02(\t\"*\n\x16StreamSendFileResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\x90\x01\n\x16StreamSendFileCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12-\n\x04info\x18\x02 \x01(\x0b\x32\x1d.livekit.proto.ByteStreamInfoH\x00\x12+\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.StreamErrorH\x00\x42\x08\n\x06result\"z\n\x15StreamSendTextRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x31\n\x07options\x18\x02 \x02(\x0b\x32 .livekit.proto.StreamTextOptions\x12\x0c\n\x04text\x18\x03 \x02(\t\"*\n\x16StreamSendTextResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\x90\x01\n\x16StreamSendTextCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12-\n\x04info\x18\x02 \x01(\x0b\x32\x1d.livekit.proto.TextStreamInfoH\x00\x12+\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.StreamErrorH\x00\x42\x08\n\x06result\"s\n\x15OwnedByteStreamWriter\x12-\n\x06handle\x18\x01 \x02(\x0b\x32\x1d.livekit.proto.FfiOwnedHandle\x12+\n\x04info\x18\x02 \x02(\x0b\x32\x1d.livekit.proto.ByteStreamInfo\"l\n\x15\x42yteStreamOpenRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x31\n\x07options\x18\x02 \x02(\x0b\x32 .livekit.proto.StreamByteOptions\"*\n\x16\x42yteStreamOpenResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\x99\x01\n\x16\x42yteStreamOpenCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x36\n\x06writer\x18\x02 \x01(\x0b\x32$.livekit.proto.OwnedByteStreamWriterH\x00\x12+\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.StreamErrorH\x00\x42\x08\n\x06result\"D\n\x1c\x42yteStreamWriterWriteRequest\x12\x15\n\rwriter_handle\x18\x01 \x02(\x04\x12\r\n\x05\x62ytes\x18\x02 \x02(\x0c\"1\n\x1d\x42yteStreamWriterWriteResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\\\n\x1d\x42yteStreamWriterWriteCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12)\n\x05\x65rror\x18\x02 \x01(\x0b\x32\x1a.livekit.proto.StreamError\"E\n\x1c\x42yteStreamWriterCloseRequest\x12\x15\n\rwriter_handle\x18\x01 \x02(\x04\x12\x0e\n\x06reason\x18\x02 \x01(\t\"1\n\x1d\x42yteStreamWriterCloseResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\\\n\x1d\x42yteStreamWriterCloseCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12)\n\x05\x65rror\x18\x02 \x01(\x0b\x32\x1a.livekit.proto.StreamError\"s\n\x15OwnedTextStreamWriter\x12-\n\x06handle\x18\x01 \x02(\x0b\x32\x1d.livekit.proto.FfiOwnedHandle\x12+\n\x04info\x18\x02 \x02(\x0b\x32\x1d.livekit.proto.TextStreamInfo\"l\n\x15TextStreamOpenRequest\x12 \n\x18local_participant_handle\x18\x01 \x02(\x04\x12\x31\n\x07options\x18\x02 \x02(\x0b\x32 .livekit.proto.StreamTextOptions\"*\n\x16TextStreamOpenResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\x99\x01\n\x16TextStreamOpenCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12\x36\n\x06writer\x18\x02 \x01(\x0b\x32$.livekit.proto.OwnedTextStreamWriterH\x00\x12+\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x1a.livekit.proto.StreamErrorH\x00\x42\x08\n\x06result\"C\n\x1cTextStreamWriterWriteRequest\x12\x15\n\rwriter_handle\x18\x01 \x02(\x04\x12\x0c\n\x04text\x18\x02 \x02(\t\"1\n\x1dTextStreamWriterWriteResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\\\n\x1dTextStreamWriterWriteCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12)\n\x05\x65rror\x18\x02 \x01(\x0b\x32\x1a.livekit.proto.StreamError\"E\n\x1cTextStreamWriterCloseRequest\x12\x15\n\rwriter_handle\x18\x01 \x02(\x04\x12\x0e\n\x06reason\x18\x02 \x01(\t\"1\n\x1dTextStreamWriterCloseResponse\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\"\\\n\x1dTextStreamWriterCloseCallback\x12\x10\n\x08\x61sync_id\x18\x01 \x02(\x04\x12)\n\x05\x65rror\x18\x02 \x01(\x0b\x32\x1a.livekit.proto.StreamError\"\xc9\x03\n\x0eTextStreamInfo\x12\x11\n\tstream_id\x18\x01 \x02(\t\x12\x11\n\ttimestamp\x18\x02 \x02(\x03\x12\x11\n\tmime_type\x18\x03 \x02(\t\x12\r\n\x05topic\x18\x04 \x02(\t\x12\x14\n\x0ctotal_length\x18\x05 \x01(\x04\x12\x41\n\nattributes\x18\x06 \x03(\x0b\x32-.livekit.proto.TextStreamInfo.AttributesEntry\x12\x43\n\x0eoperation_type\x18\x07 \x02(\x0e\x32+.livekit.proto.TextStreamInfo.OperationType\x12\x0f\n\x07version\x18\x08 \x01(\x05\x12\x1a\n\x12reply_to_stream_id\x18\t \x01(\t\x12\x1b\n\x13\x61ttached_stream_ids\x18\n \x03(\t\x12\x11\n\tgenerated\x18\x0b \x01(\x08\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"A\n\rOperationType\x12\n\n\x06\x43REATE\x10\x00\x12\n\n\x06UPDATE\x10\x01\x12\n\n\x06\x44\x45LETE\x10\x02\x12\x0c\n\x08REACTION\x10\x03\"\xf2\x01\n\x0e\x42yteStreamInfo\x12\x11\n\tstream_id\x18\x01 \x02(\t\x12\x11\n\ttimestamp\x18\x02 \x02(\x03\x12\x11\n\tmime_type\x18\x03 \x02(\t\x12\r\n\x05topic\x18\x04 \x02(\t\x12\x14\n\x0ctotal_length\x18\x05 \x01(\x04\x12\x41\n\nattributes\x18\x06 \x03(\x0b\x32-.livekit.proto.ByteStreamInfo.AttributesEntry\x12\x0c\n\x04name\x18\x07 \x02(\t\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xe9\x02\n\x11StreamTextOptions\x12\r\n\x05topic\x18\x01 \x02(\t\x12\x44\n\nattributes\x18\x02 \x03(\x0b\x32\x30.livekit.proto.StreamTextOptions.AttributesEntry\x12\x1e\n\x16\x64\x65stination_identities\x18\x03 \x03(\t\x12\n\n\x02id\x18\x04 \x01(\t\x12\x43\n\x0eoperation_type\x18\x05 \x01(\x0e\x32+.livekit.proto.TextStreamInfo.OperationType\x12\x0f\n\x07version\x18\x06 \x01(\x05\x12\x1a\n\x12reply_to_stream_id\x18\x07 \x01(\t\x12\x1b\n\x13\x61ttached_stream_ids\x18\x08 \x03(\t\x12\x11\n\tgenerated\x18\t \x01(\x08\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xfe\x01\n\x11StreamByteOptions\x12\r\n\x05topic\x18\x01 \x02(\t\x12\x44\n\nattributes\x18\x02 \x03(\x0b\x32\x30.livekit.proto.StreamByteOptions.AttributesEntry\x12\x1e\n\x16\x64\x65stination_identities\x18\x03 \x03(\t\x12\n\n\x02id\x18\x04 \x01(\t\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x11\n\tmime_type\x18\x06 \x01(\t\x12\x14\n\x0ctotal_length\x18\x07 \x01(\x04\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\"\n\x0bStreamError\x12\x13\n\x0b\x64\x65scription\x18\x01 \x02(\tB\x10\xaa\x02\rLiveKit.Proto')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'data_stream_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\252\002\rLiveKit.Proto'
  _globals['_TEXTSTREAMINFO_ATTRIBUTESENTRY']._options = None
  _globals['_TEXTSTREAMINFO_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_BYTESTREAMINFO_ATTRIBUTESENTRY']._options = None
  _globals['_BYTESTREAMINFO_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_STREAMTEXTOPTIONS_ATTRIBUTESENTRY']._options = None
  _globals['_STREAMTEXTOPTIONS_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_STREAMBYTEOPTIONS_ATTRIBUTESENTRY']._options = None
  _globals['_STREAMBYTEOPTIONS_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_OWNEDTEXTSTREAMREADER']._serialized_start=50
  _globals['_OWNEDTEXTSTREAMREADER']._serialized_end=165
  _globals['_TEXTSTREAMREADERREADINCREMENTALREQUEST']._serialized_start=167
  _globals['_TEXTSTREAMREADERREADINCREMENTALREQUEST']._serialized_end=230
  _globals['_TEXTSTREAMREADERREADINCREMENTALRESPONSE']._serialized_start=232
  _globals['_TEXTSTREAMREADERREADINCREMENTALRESPONSE']._serialized_end=273
  _globals['_TEXTSTREAMREADERREADALLREQUEST']._serialized_start=275
  _globals['_TEXTSTREAMREADERREADALLREQUEST']._serialized_end=330
  _globals['_TEXTSTREAMREADERREADALLRESPONSE']._serialized_start=332
  _globals['_TEXTSTREAMREADERREADALLRESPONSE']._serialized_end=383
  _globals['_TEXTSTREAMREADERREADALLCALLBACK']._serialized_start=385
  _globals['_TEXTSTREAMREADERREADALLCALLBACK']._serialized_end=510
  _globals['_TEXTSTREAMREADEREVENT']._serialized_start=513
  _globals['_TEXTSTREAMREADEREVENT']._serialized_end=692
  _globals['_TEXTSTREAMREADERCHUNKRECEIVED']._serialized_start=694
  _globals['_TEXTSTREAMREADERCHUNKRECEIVED']._serialized_end=742
  _globals['_TEXTSTREAMREADEREOS']._serialized_start=744
  _globals['_TEXTSTREAMREADEREOS']._serialized_end=808
  _globals['_OWNEDBYTESTREAMREADER']._serialized_start=810
  _globals['_OWNEDBYTESTREAMREADER']._serialized_end=925
  _globals['_BYTESTREAMREADERREADINCREMENTALREQUEST']._serialized_start=927
  _globals['_BYTESTREAMREADERREADINCREMENTALREQUEST']._serialized_end=990
  _globals['_BYTESTREAMREADERREADINCREMENTALRESPONSE']._serialized_start=992
  _globals['_BYTESTREAMREADERREADINCREMENTALRESPONSE']._serialized_end=1033
  _globals['_BYTESTREAMREADERREADALLREQUEST']._serialized_start=1035
  _globals['_BYTESTREAMREADERREADALLREQUEST']._serialized_end=1090
  _globals['_BYTESTREAMREADERREADALLRESPONSE']._serialized_start=1092
  _globals['_BYTESTREAMREADERREADALLRESPONSE']._serialized_end=1143
  _globals['_BYTESTREAMREADERREADALLCALLBACK']._serialized_start=1145
  _globals['_BYTESTREAMREADERREADALLCALLBACK']._serialized_end=1270
  _globals['_BYTESTREAMREADERWRITETOFILEREQUEST']._serialized_start=1272
  _globals['_BYTESTREAMREADERWRITETOFILEREQUEST']._serialized_end=1373
  _globals['_BYTESTREAMREADERWRITETOFILERESPONSE']._serialized_start=1375
  _globals['_BYTESTREAMREADERWRITETOFILERESPONSE']._serialized_end=1430
  _globals['_BYTESTREAMREADERWRITETOFILECALLBACK']._serialized_start=1433
  _globals['_BYTESTREAMREADERWRITETOFILECALLBACK']._serialized_end=1564
  _globals['_BYTESTREAMREADEREVENT']._serialized_start=1567
  _globals['_BYTESTREAMREADEREVENT']._serialized_end=1746
  _globals['_BYTESTREAMREADERCHUNKRECEIVED']._serialized_start=1748
  _globals['_BYTESTREAMREADERCHUNKRECEIVED']._serialized_end=1796
  _globals['_BYTESTREAMREADEREOS']._serialized_start=1798
  _globals['_BYTESTREAMREADEREOS']._serialized_end=1862
  _globals['_STREAMSENDFILEREQUEST']._serialized_start=1864
  _globals['_STREAMSENDFILEREQUEST']._serialized_end=1991
  _globals['_STREAMSENDFILERESPONSE']._serialized_start=1993
  _globals['_STREAMSENDFILERESPONSE']._serialized_end=2035
  _globals['_STREAMSENDFILECALLBACK']._serialized_start=2038
  _globals['_STREAMSENDFILECALLBACK']._serialized_end=2182
  _globals['_STREAMSENDTEXTREQUEST']._serialized_start=2184
  _globals['_STREAMSENDTEXTREQUEST']._serialized_end=2306
  _globals['_STREAMSENDTEXTRESPONSE']._serialized_start=2308
  _globals['_STREAMSENDTEXTRESPONSE']._serialized_end=2350
  _globals['_STREAMSENDTEXTCALLBACK']._serialized_start=2353
  _globals['_STREAMSENDTEXTCALLBACK']._serialized_end=2497
  _globals['_OWNEDBYTESTREAMWRITER']._serialized_start=2499
  _globals['_OWNEDBYTESTREAMWRITER']._serialized_end=2614
  _globals['_BYTESTREAMOPENREQUEST']._serialized_start=2616
  _globals['_BYTESTREAMOPENREQUEST']._serialized_end=2724
  _globals['_BYTESTREAMOPENRESPONSE']._serialized_start=2726
  _globals['_BYTESTREAMOPENRESPONSE']._serialized_end=2768
  _globals['_BYTESTREAMOPENCALLBACK']._serialized_start=2771
  _globals['_BYTESTREAMOPENCALLBACK']._serialized_end=2924
  _globals['_BYTESTREAMWRITERWRITEREQUEST']._serialized_start=2926
  _globals['_BYTESTREAMWRITERWRITEREQUEST']._serialized_end=2994
  _globals['_BYTESTREAMWRITERWRITERESPONSE']._serialized_start=2996
  _globals['_BYTESTREAMWRITERWRITERESPONSE']._serialized_end=3045
  _globals['_BYTESTREAMWRITERWRITECALLBACK']._serialized_start=3047
  _globals['_BYTESTREAMWRITERWRITECALLBACK']._serialized_end=3139
  _globals['_BYTESTREAMWRITERCLOSEREQUEST']._serialized_start=3141
  _globals['_BYTESTREAMWRITERCLOSEREQUEST']._serialized_end=3210
  _globals['_BYTESTREAMWRITERCLOSERESPONSE']._serialized_start=3212
  _globals['_BYTESTREAMWRITERCLOSERESPONSE']._serialized_end=3261
  _globals['_BYTESTREAMWRITERCLOSECALLBACK']._serialized_start=3263
  _globals['_BYTESTREAMWRITERCLOSECALLBACK']._serialized_end=3355
  _globals['_OWNEDTEXTSTREAMWRITER']._serialized_start=3357
  _globals['_OWNEDTEXTSTREAMWRITER']._serialized_end=3472
  _globals['_TEXTSTREAMOPENREQUEST']._serialized_start=3474
  _globals['_TEXTSTREAMOPENREQUEST']._serialized_end=3582
  _globals['_TEXTSTREAMOPENRESPONSE']._serialized_start=3584
  _globals['_TEXTSTREAMOPENRESPONSE']._serialized_end=3626
  _globals['_TEXTSTREAMOPENCALLBACK']._serialized_start=3629
  _globals['_TEXTSTREAMOPENCALLBACK']._serialized_end=3782
  _globals['_TEXTSTREAMWRITERWRITEREQUEST']._serialized_start=3784
  _globals['_TEXTSTREAMWRITERWRITEREQUEST']._serialized_end=3851
  _globals['_TEXTSTREAMWRITERWRITERESPONSE']._serialized_start=3853
  _globals['_TEXTSTREAMWRITERWRITERESPONSE']._serialized_end=3902
  _globals['_TEXTSTREAMWRITERWRITECALLBACK']._serialized_start=3904
  _globals['_TEXTSTREAMWRITERWRITECALLBACK']._serialized_end=3996
  _globals['_TEXTSTREAMWRITERCLOSEREQUEST']._serialized_start=3998
  _globals['_TEXTSTREAMWRITERCLOSEREQUEST']._serialized_end=4067
  _globals['_TEXTSTREAMWRITERCLOSERESPONSE']._serialized_start=4069
  _globals['_TEXTSTREAMWRITERCLOSERESPONSE']._serialized_end=4118
  _globals['_TEXTSTREAMWRITERCLOSECALLBACK']._serialized_start=4120
  _globals['_TEXTSTREAMWRITERCLOSECALLBACK']._serialized_end=4212
  _globals['_TEXTSTREAMINFO']._serialized_start=4215
  _globals['_TEXTSTREAMINFO']._serialized_end=4672
  _globals['_TEXTSTREAMINFO_ATTRIBUTESENTRY']._serialized_start=4556
  _globals['_TEXTSTREAMINFO_ATTRIBUTESENTRY']._serialized_end=4605
  _globals['_TEXTSTREAMINFO_OPERATIONTYPE']._serialized_start=4607
  _globals['_TEXTSTREAMINFO_OPERATIONTYPE']._serialized_end=4672
  _globals['_BYTESTREAMINFO']._serialized_start=4675
  _globals['_BYTESTREAMINFO']._serialized_end=4917
  _globals['_BYTESTREAMINFO_ATTRIBUTESENTRY']._serialized_start=4556
  _globals['_BYTESTREAMINFO_ATTRIBUTESENTRY']._serialized_end=4605
  _globals['_STREAMTEXTOPTIONS']._serialized_start=4920
  _globals['_STREAMTEXTOPTIONS']._serialized_end=5281
  _globals['_STREAMTEXTOPTIONS_ATTRIBUTESENTRY']._serialized_start=4556
  _globals['_STREAMTEXTOPTIONS_ATTRIBUTESENTRY']._serialized_end=4605
  _globals['_STREAMBYTEOPTIONS']._serialized_start=5284
  _globals['_STREAMBYTEOPTIONS']._serialized_end=5538
  _globals['_STREAMBYTEOPTIONS_ATTRIBUTESENTRY']._serialized_start=4556
  _globals['_STREAMBYTEOPTIONS_ATTRIBUTESENTRY']._serialized_end=4605
  _globals['_STREAMERROR']._serialized_start=5540
  _globals['_STREAMERROR']._serialized_end=5574
# @@protoc_insertion_point(module_scope)
