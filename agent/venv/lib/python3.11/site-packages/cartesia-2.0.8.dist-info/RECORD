cartesia-2.0.8.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
cartesia-2.0.8.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
cartesia-2.0.8.dist-info/METADATA,sha256=xsIV3LGzfwSIXnjAGT1sotSNwN0ZaCgefCckc2EshLE,20804
cartesia-2.0.8.dist-info/RECORD,,
cartesia-2.0.8.dist-info/WHEEL,sha256=Zb28QaM1gQi8f4VCBhsUklF61CTlNYfs9YAZn-TOGFk,88
cartesia/__init__.py,sha256=P8YXd1NsmEHQOF4p0MpPMGLOSy_0cIPHOnFe-iV94oU,10311
cartesia/__pycache__/__init__.cpython-311.pyc,,
cartesia/__pycache__/base_client.cpython-311.pyc,,
cartesia/__pycache__/client.cpython-311.pyc,,
cartesia/__pycache__/environment.cpython-311.pyc,,
cartesia/__pycache__/version.cpython-311.pyc,,
cartesia/api_status/__init__.py,sha256=_dHNLdknrBjxHtU2PvLumttJM-JTQhJQqhhAQkLqt_U,168
cartesia/api_status/__pycache__/__init__.cpython-311.pyc,,
cartesia/api_status/__pycache__/client.cpython-311.pyc,,
cartesia/api_status/client.py,sha256=GJ9Dq8iCn3hn8vCIqc6k1fCGEhSz0T0kaPGcdFnbMDY,3146
cartesia/api_status/requests/__init__.py,sha256=ilEMzEy1JEw484CuL92bX5lHGOznc62pjiDMgiZ0tKM,130
cartesia/api_status/requests/__pycache__/__init__.cpython-311.pyc,,
cartesia/api_status/requests/__pycache__/api_info.cpython-311.pyc,,
cartesia/api_status/requests/api_info.py,sha256=AmB6RpquI2yUlTQBtOk8e0qtLmXHYLcGZKpXZahOwmc,172
cartesia/api_status/types/__init__.py,sha256=6NUyGWiGK1Wl3mXlSMJN2ObKf2LK3vjX2MUP1uopfEQ,118
cartesia/api_status/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/api_status/types/__pycache__/api_info.cpython-311.pyc,,
cartesia/api_status/types/api_info.py,sha256=o1LwSxnoHpCR7huw9J-cF6LRlC_fiftDQLYUz8p-vTc,568
cartesia/auth/__init__.py,sha256=T8_EGgzdzyJLqfD7DAgdkE6G1Ey2sUMyze-7x8HTzGg,355
cartesia/auth/__pycache__/__init__.cpython-311.pyc,,
cartesia/auth/__pycache__/client.cpython-311.pyc,,
cartesia/auth/client.py,sha256=0lPsR37IPxOJ-_2LUIR1CFYg65oWhMkx6sSCiQhiLBM,5527
cartesia/auth/requests/__init__.py,sha256=hR7qCSJCPiOG7f8z8jTKQLOC7QoonSvvPKe0JbcEYEs,278
cartesia/auth/requests/__pycache__/__init__.cpython-311.pyc,,
cartesia/auth/requests/__pycache__/token_grant.cpython-311.pyc,,
cartesia/auth/requests/__pycache__/token_request.cpython-311.pyc,,
cartesia/auth/requests/__pycache__/token_response.cpython-311.pyc,,
cartesia/auth/requests/token_grant.py,sha256=LWcPEP1BZZGII_Wgu5pScN33aS4E9WoCX2qIyadjbQw,445
cartesia/auth/requests/token_request.py,sha256=z8PXbTVokCWM53MhUhXLcCK9UcGWhh4P6rO1fuGDuBw,608
cartesia/auth/requests/token_response.py,sha256=jXpHZmFe6RWO837e_lC2GJWwqO-b6KHOA-b6tTJVC54,211
cartesia/auth/types/__init__.py,sha256=iZrkHrlWs8e9KkR27f2IG-B72HC_N05A7Lcyt_EU9SM,242
cartesia/auth/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/auth/types/__pycache__/token_grant.cpython-311.pyc,,
cartesia/auth/types/__pycache__/token_request.cpython-311.pyc,,
cartesia/auth/types/__pycache__/token_response.cpython-311.pyc,,
cartesia/auth/types/token_grant.py,sha256=vhmadI9McsWwXormWBhKJ2XvpXONnwXKzfgNsfaBG30,850
cartesia/auth/types/token_request.py,sha256=5gDJe-ULgY-JnNd4ZmgbmUjmboW4B4ymLjobAlQ4qNM,1001
cartesia/auth/types/token_response.py,sha256=_GcvfQdjwgNu1ODj8EuTkaMsez508a6xuOo8HOVNOJQ,626
cartesia/base_client.py,sha256=igAZOMDXz2Nv58oXHa7I9UfgxVN48drqhEmfsCCQlg8,6701
cartesia/client.py,sha256=LoJjlJW2kJA-hyDt-Wu7QuKQsiTiLQfLYZjsjtewPJM,6537
cartesia/core/__init__.py,sha256=-t9txgeQZL_1FDw_08GEoj4ft1Cn9Dti6X0Drsadlr0,1519
cartesia/core/__pycache__/__init__.cpython-311.pyc,,
cartesia/core/__pycache__/api_error.cpython-311.pyc,,
cartesia/core/__pycache__/client_wrapper.cpython-311.pyc,,
cartesia/core/__pycache__/datetime_utils.cpython-311.pyc,,
cartesia/core/__pycache__/file.cpython-311.pyc,,
cartesia/core/__pycache__/http_client.cpython-311.pyc,,
cartesia/core/__pycache__/jsonable_encoder.cpython-311.pyc,,
cartesia/core/__pycache__/pagination.cpython-311.pyc,,
cartesia/core/__pycache__/pydantic_utilities.cpython-311.pyc,,
cartesia/core/__pycache__/query_encoder.cpython-311.pyc,,
cartesia/core/__pycache__/remove_none_from_dict.cpython-311.pyc,,
cartesia/core/__pycache__/request_options.cpython-311.pyc,,
cartesia/core/__pycache__/serialization.cpython-311.pyc,,
cartesia/core/api_error.py,sha256=RE8LELok2QCjABadECTvtDp7qejA1VmINCh6TbqPwSE,426
cartesia/core/client_wrapper.py,sha256=JoQT3EFinsagzASKi5rc7WDSyStOFPy9Mgy6h0w46Pc,1854
cartesia/core/datetime_utils.py,sha256=nBys2IsYrhPdszxGKCNRPSOCwa-5DWOHG95FB8G9PKo,1047
cartesia/core/file.py,sha256=d4NNbX8XvXP32z8KpK2Xovv33nFfruIrpz0QWxlgpZk,2663
cartesia/core/http_client.py,sha256=KL5RGa0y4n8nX0-07WRg4ZQUTq30sc-XJbWcP5vjBDg,19552
cartesia/core/jsonable_encoder.py,sha256=qaF1gtgH-kQZb4kJskETwcCsOPUof-NnYVdszHkb-dM,3656
cartesia/core/pagination.py,sha256=ykffinbn7c3pXaXLrAlFbSdNbZmJJXoU7VSFIRCLAo0,3177
cartesia/core/pydantic_utilities.py,sha256=UibVGGYmBDsV834x8CtckRDrTIL4lYJPMrcq9yvf7RM,11973
cartesia/core/query_encoder.py,sha256=ekulqNd0j8TgD7ox-Qbz7liqX8-KP9blvT9DsRCenYM,2144
cartesia/core/remove_none_from_dict.py,sha256=EU9SGgYidWq7SexuJbNs4-PZ-5Bl3Vppd864mS6vQZw,342
cartesia/core/request_options.py,sha256=h0QUNCFVdCW_7GclVySCAY2w4NhtXVBUCmHgmzaxpcg,1681
cartesia/core/serialization.py,sha256=D9h_t-RQON3-CHWs1C4ESY9B-Yd5d-l5lnTLb_X896g,9601
cartesia/datasets/__init__.py,sha256=m7uI_lBRsIh-YOIin7Q11cHSXD4FMUkq_J74CLMWuyY,640
cartesia/datasets/__pycache__/__init__.cpython-311.pyc,,
cartesia/datasets/requests/__init__.py,sha256=pxNlVfjSEPr1RfJKb07CXKIrg_EDa4_t8le2x6u9l1c,489
cartesia/datasets/requests/__pycache__/__init__.cpython-311.pyc,,
cartesia/datasets/requests/__pycache__/create_dataset_request.cpython-311.pyc,,
cartesia/datasets/requests/__pycache__/dataset.cpython-311.pyc,,
cartesia/datasets/requests/__pycache__/dataset_file.cpython-311.pyc,,
cartesia/datasets/requests/__pycache__/paginated_dataset_files.cpython-311.pyc,,
cartesia/datasets/requests/__pycache__/paginated_datasets.cpython-311.pyc,,
cartesia/datasets/requests/create_dataset_request.py,sha256=RtISvq-eA8GG_0plEPTkJl8TzOVm93NQuHbJVGtkQeg,169
cartesia/datasets/requests/dataset.py,sha256=Wxg5AEBh8h9t5N2ud96bsrPVgjH5orMAv1PXl1RGTDs,188
cartesia/datasets/requests/dataset_file.py,sha256=ddxUw-yYzATfRDn_ey-SzSYd_2J501OsAggH13sbIRE,196
cartesia/datasets/requests/paginated_dataset_files.py,sha256=6hZm3O8qJSkinqdJLSgEPaKBi2Vx3NxfTKk1FGwA2e0,278
cartesia/datasets/requests/paginated_datasets.py,sha256=UHzcmKJ73pOtN-CUfQurHCb0y6yf1hNGum3ckbn83YY,261
cartesia/datasets/types/__init__.py,sha256=cDPaVpLq8O6I6mWuuVGZOICgO9_Os2BXPWEAK1hbHsg,486
cartesia/datasets/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/datasets/types/__pycache__/create_dataset_request.cpython-311.pyc,,
cartesia/datasets/types/__pycache__/dataset.cpython-311.pyc,,
cartesia/datasets/types/__pycache__/dataset_file.cpython-311.pyc,,
cartesia/datasets/types/__pycache__/file_purpose.cpython-311.pyc,,
cartesia/datasets/types/__pycache__/paginated_dataset_files.cpython-311.pyc,,
cartesia/datasets/types/__pycache__/paginated_datasets.cpython-311.pyc,,
cartesia/datasets/types/create_dataset_request.py,sha256=w9-7U1gcW67tcticQ9zteYga3Vc_LPUfISttLc_-06o,565
cartesia/datasets/types/dataset.py,sha256=uR_tSITVL_G2lYqMZIhPGddgmaUbqsifZ5eXpbt-pOE,584
cartesia/datasets/types/dataset_file.py,sha256=VuKeojvfl112AZzN3qmMiI3P_oeNXq84U4dAIfuwY3c,592
cartesia/datasets/types/file_purpose.py,sha256=YfhGIUTJsQedSf6lnBU-jSz_h9xEpVVtOyYnyHNtcaQ,148
cartesia/datasets/types/paginated_dataset_files.py,sha256=xY2XiXvcLqt5nPQIIPuIwBtPeM0_0sVYxc5MshFh5j8,644
cartesia/datasets/types/paginated_datasets.py,sha256=bNgCtzFC3EPqAc0bd9nvkKtR4Q8CwJNpVUr0BBpQJkE,627
cartesia/embedding/__init__.py,sha256=mfTGlVbrByIrk4KTsZeNCNfm2qYUu1dcO_o37eEgPik,119
cartesia/embedding/__pycache__/__init__.cpython-311.pyc,,
cartesia/embedding/types/__init__.py,sha256=aOrEOGuiO6dlSGu7pckqVMTYEMVAR5I7qqcairy0vlA,123
cartesia/embedding/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/embedding/types/__pycache__/embedding.cpython-311.pyc,,
cartesia/embedding/types/embedding.py,sha256=C1OJg8M4T1Apfcv4qx79ndftg0SgH4Lfqe_iU3UF-bA,1851
cartesia/environment.py,sha256=Qnp91BGLic7hXmKsiYub2m3nPfvDWm59aB1wWta1J6A,160
cartesia/infill/__init__.py,sha256=FTtvy8EDg9nNNg9WCatVgKTRYV8-_v1roeGPAKoa_pw,65
cartesia/infill/__pycache__/__init__.cpython-311.pyc,,
cartesia/infill/__pycache__/client.cpython-311.pyc,,
cartesia/infill/client.py,sha256=uEDhE3Cx47ZyG7ofR-GOR0LhHiHeTLkUcjkLSsyU2ug,12563
cartesia/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cartesia/stt/__init__.py,sha256=UHT5OM-5phGwLCckL8BXGdC3QepJoboScW5eSXUE2S4,1763
cartesia/stt/__pycache__/__init__.cpython-311.pyc,,
cartesia/stt/__pycache__/_async_websocket.cpython-311.pyc,,
cartesia/stt/__pycache__/_websocket.cpython-311.pyc,,
cartesia/stt/__pycache__/client.cpython-311.pyc,,
cartesia/stt/__pycache__/socket_client.cpython-311.pyc,,
cartesia/stt/_async_websocket.py,sha256=6MVYvSz3d9sI5-zzT_aIPEFKxXeCQU00RYFpYSF0dio,12385
cartesia/stt/_websocket.py,sha256=lNu7BzhkRswJrzji4l5OhJbceQPR4ybBPY-QTsfk11M,12328
cartesia/stt/client.py,sha256=ROFjsSSfnot02CSFrrL1FYgMNL_TWEhnBg5PVjAQs-g,15569
cartesia/stt/requests/__init__.py,sha256=xVvSrY-imcWR3ecyMGIx9JXORpxWAGDUCSDuz2zbJNE,1123
cartesia/stt/requests/__pycache__/__init__.cpython-311.pyc,,
cartesia/stt/requests/__pycache__/done_message.cpython-311.pyc,,
cartesia/stt/requests/__pycache__/error_message.cpython-311.pyc,,
cartesia/stt/requests/__pycache__/flush_done_message.cpython-311.pyc,,
cartesia/stt/requests/__pycache__/streaming_transcription_response.cpython-311.pyc,,
cartesia/stt/requests/__pycache__/transcript_message.cpython-311.pyc,,
cartesia/stt/requests/__pycache__/transcription_response.cpython-311.pyc,,
cartesia/stt/requests/__pycache__/transcription_word.cpython-311.pyc,,
cartesia/stt/requests/done_message.py,sha256=7kCw8FrTZ6C5MS1xTkCOonYPcaqttbzG6TRvMj_-Z8I,388
cartesia/stt/requests/error_message.py,sha256=AbVs03q-Rdp2AFmaJqj-b3RshYqhIQeSiQO3zOkFdiA,395
cartesia/stt/requests/flush_done_message.py,sha256=zCMsHdoXXsBS8jz-ZAIRkEyAsbgqYCI5jPdBHEBFHLY,394
cartesia/stt/requests/streaming_transcription_response.py,sha256=uO9rxPyjDtcxCAoVHNi6ioovkwfpcxNFOsyc9YfOM6M,1298
cartesia/stt/requests/transcript_message.py,sha256=rwn7zhJ3kw-90wjOAUImnROkQyjdHUKHr_SGxxLg4G0,1244
cartesia/stt/requests/transcription_response.py,sha256=8lBqQCxLW1zGHacPtaMTgoKKNpyMtNqMUNAhQJfeKus,779
cartesia/stt/requests/transcription_word.py,sha256=RTyiOLIqw-BQpTsU5LRwx0MY8Do6tHiOP0RYQWOGkHQ,350
cartesia/stt/socket_client.py,sha256=JxSeJ_CwUELZhHT0_CcREjKj1I55KTtvyjB4gNkiPUI,5294
cartesia/stt/types/__init__.py,sha256=j8E4ZkXeKzRhhjLEs55iOtHL60mffD5Ug1exY88zkpY,1132
cartesia/stt/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/stt/types/__pycache__/done_message.cpython-311.pyc,,
cartesia/stt/types/__pycache__/error_message.cpython-311.pyc,,
cartesia/stt/types/__pycache__/flush_done_message.cpython-311.pyc,,
cartesia/stt/types/__pycache__/streaming_transcription_response.cpython-311.pyc,,
cartesia/stt/types/__pycache__/stt_encoding.cpython-311.pyc,,
cartesia/stt/types/__pycache__/timestamp_granularity.cpython-311.pyc,,
cartesia/stt/types/__pycache__/transcript_message.cpython-311.pyc,,
cartesia/stt/types/__pycache__/transcription_response.cpython-311.pyc,,
cartesia/stt/types/__pycache__/transcription_word.cpython-311.pyc,,
cartesia/stt/types/done_message.py,sha256=F3Upci6Lm3Wcd_Ga4Uxxw9WO7XB3y54hrvR1cFqPxlo,803
cartesia/stt/types/error_message.py,sha256=7HmoN7pBymPvJSB9JHwvMp3iXP-4xxd-ATpr-U68ocA,802
cartesia/stt/types/flush_done_message.py,sha256=uaDC6IBtZQ9ahPS575ezZiH6neR5JDnaI9KOemCeIwI,809
cartesia/stt/types/streaming_transcription_response.py,sha256=MzwCU-0TYGNoh2OqsiNdYjSuKQcqAd3JvhS8UVAoz2A,3179
cartesia/stt/types/stt_encoding.py,sha256=cdWl5cN8t-nX07337tkCt4X-ZNObUlsPyv2cl9phxpE,218
cartesia/stt/types/timestamp_granularity.py,sha256=Oe39JvLeMgR2BIJnx32abhvs05dJeKuZ1xx73n224HA,152
cartesia/stt/types/transcript_message.py,sha256=J-MchlahI96nVBiMSLJrEOXFw2pBShbMXVocysQRnrY,1693
cartesia/stt/types/transcription_response.py,sha256=QMcD6eLmp_Z2uaRLVyxYYIdoiRiVSGhBoxN3kjRTK2I,1190
cartesia/stt/types/transcription_word.py,sha256=yxTndKXNmToPOM6_F_QfF-B0dE6Kx8-UwBpHLj2_zWk,803
cartesia/tts/__init__.py,sha256=DwNzIilOcdNUbeIHIknngnW8WyZ6K5xZremSQQoo5VM,4927
cartesia/tts/__pycache__/__init__.cpython-311.pyc,,
cartesia/tts/__pycache__/_async_websocket.cpython-311.pyc,,
cartesia/tts/__pycache__/_websocket.cpython-311.pyc,,
cartesia/tts/__pycache__/client.cpython-311.pyc,,
cartesia/tts/__pycache__/socket_client.cpython-311.pyc,,
cartesia/tts/_async_websocket.py,sha256=yviop52kcrW490JSV8jdxSQITmbQzb0TOxNd1zmuZsg,19424
cartesia/tts/_websocket.py,sha256=K93vHOdxhF4-Duk8xunNnIpvkAT_ztfAtaomD5im8c0,19247
cartesia/tts/client.py,sha256=Oot_ctyaqBgRMpyBUaMwh3z1M62oPKVMXNvMkmo1fRw,18180
cartesia/tts/requests/__init__.py,sha256=SeITRF5QSAjOE5pNxbD6VffwwttMnQwuv0Z5n9h7BKs,3418
cartesia/tts/requests/__pycache__/__init__.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/cancel_context_request.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/controls.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/generation_request.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/mp_3_output_format.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/output_format.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/phoneme_timestamps.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/raw_output_format.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/speed.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/sse_output_format.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/tts_request.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/tts_request_embedding_specifier.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/tts_request_id_specifier.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/tts_request_voice_specifier.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/ttssse_request.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/wav_output_format.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_base_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_chunk_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_done_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_error_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_flush_done_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_phoneme_timestamps_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_raw_output_format.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_request.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_stream_options.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_timestamps_response.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_tts_output.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/web_socket_tts_request.cpython-311.pyc,,
cartesia/tts/requests/__pycache__/word_timestamps.cpython-311.pyc,,
cartesia/tts/requests/cancel_context_request.py,sha256=Wl8g-o5vwl9ENm-H1wsLx441FkIR_4Wt5UYtuWce2Yw,431
cartesia/tts/requests/controls.py,sha256=xzUJlfgqhaJ1A-JD0LTpoHYk4iEpCuGpSD7qE4YYsRg,285
cartesia/tts/requests/generation_request.py,sha256=JQPumk0UMCHDQrcUvuqeDsdc8LCJAEolSs10LpJzK00,3083
cartesia/tts/requests/mp_3_output_format.py,sha256=PGDVzC1d7-Jce12rFxtF8G1pTHmlUdiGAhykFTABg0w,316
cartesia/tts/requests/output_format.py,sha256=8TKu9AAeHCR5L4edzYch8FIYIldn4bM7ySrsCl8W_g8,842
cartesia/tts/requests/phoneme_timestamps.py,sha256=ft81nmqElZAnvTBT27lY6YWfF18ZGsCx3Y1XHv9J7cM,267
cartesia/tts/requests/raw_output_format.py,sha256=S60Vp7DeAATCMLF3bXgxhw0zILJBWJ9GhI9irAg_UkI,316
cartesia/tts/requests/speed.py,sha256=-YGBWwh7_VtCBnYlT5EVsnrmcHFMEBTxy9LathZhkMA,259
cartesia/tts/requests/sse_output_format.py,sha256=z_f7dlDYNvpheYOSnf3lOslHF40vS852pYkxHTpqAcc,293
cartesia/tts/requests/tts_request.py,sha256=KBoahYfPbDENlEWsqnR4z1ZIhGIJwhLrzQIzkbtqtzE,1021
cartesia/tts/requests/tts_request_embedding_specifier.py,sha256=-M54ZjV0H5LPwcKtz0bOVqlkvO1pPiMbqMbVBMko3Ns,565
cartesia/tts/requests/tts_request_id_specifier.py,sha256=-0ClfyJnnaH0uAcF5r84s3cM_cw2wT39dp6T4JYzOQ8,536
cartesia/tts/requests/tts_request_voice_specifier.py,sha256=eGzL4aVGq4gKPxeglsV7-wuhxg8x33Qth3uFTTytgeI,337
cartesia/tts/requests/ttssse_request.py,sha256=S8EkuEtveOetkcydinfLr5lS66PYpLQTNesyRIf_LwI,2007
cartesia/tts/requests/wav_output_format.py,sha256=qiipmT5hWsa8J-fwW1EH_rnUAX_zOUpGJUNzuLc65r4,181
cartesia/tts/requests/web_socket_base_response.py,sha256=zCjHw-FaNJMOcHiAb2NQWrBBfrzU5rc95vqDp7y9RmA,315
cartesia/tts/requests/web_socket_chunk_response.py,sha256=4fVPJH-ZZb8lJKwqyYGx5wyeYWzfuThGxMRXC6ku4bA,233
cartesia/tts/requests/web_socket_done_response.py,sha256=YLHrT6NkmDntBSxF-JGlXSavdlOWo_cb9tGKCVivGH4,206
cartesia/tts/requests/web_socket_error_response.py,sha256=ek2O5Whlzn5Ma40NhYviVl3aJBVeCA8BBvbJPUYxEiQ,213
cartesia/tts/requests/web_socket_flush_done_response.py,sha256=gP3fSWhEFVzdzBweUmVKo7JvdREW3TM9R6o9-u6V6FQ,282
cartesia/tts/requests/web_socket_phoneme_timestamps_response.py,sha256=nDRK7wo4s6R7ayJrw-LJX9WCaW4mti0HAV4X5j7cxjI,370
cartesia/tts/requests/web_socket_raw_output_format.py,sha256=9BJHE5l5bzmYCYuUoACRhbZdJBijnSiwkbR8K4EzPDY,302
cartesia/tts/requests/web_socket_request.py,sha256=5xfE0NgkBEZdus_vC-3RVQkuqhNmXHxLMX4DW3ezcKc,290
cartesia/tts/requests/web_socket_response.py,sha256=kS46YN94ilUn4qjpt1TpauZApe0N8PpAefT87jFiusY,2079
cartesia/tts/requests/web_socket_stream_options.py,sha256=VIvblFw9hGZvDzFpOnC11G0NvrFSVt-1-0sY5rpcZPI,232
cartesia/tts/requests/web_socket_timestamps_response.py,sha256=MK3zN2Q_PVWJtX5DidNB0uXoF2o33rv6qCYPVaourxY,351
cartesia/tts/requests/web_socket_tts_output.py,sha256=pX2uf0XVdziFhXCydwLlVOWb-LvBiuq-cBI6R1INiMg,760
cartesia/tts/requests/web_socket_tts_request.py,sha256=1jdRjRAO7z-KLOyp8FcDoQh933RGt-ZPR3E8Vz3XPnQ,1795
cartesia/tts/requests/word_timestamps.py,sha256=WMfBJtETi6wTpES0pYZCFfFRfEbzWE-RtosDJ5seUWg,261
cartesia/tts/socket_client.py,sha256=zTPayHbgy-yQQ50AE1HXN4GMyanisZcLXf7Ds1paYks,11621
cartesia/tts/types/__init__.py,sha256=rXphJ9b9nSYYrepr2ssG6ghtQAOQBQcLegxbl-XG3tw,3438
cartesia/tts/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/tts/types/__pycache__/cancel_context_request.cpython-311.pyc,,
cartesia/tts/types/__pycache__/context_id.cpython-311.pyc,,
cartesia/tts/types/__pycache__/controls.cpython-311.pyc,,
cartesia/tts/types/__pycache__/emotion.cpython-311.pyc,,
cartesia/tts/types/__pycache__/flush_id.cpython-311.pyc,,
cartesia/tts/types/__pycache__/generation_request.cpython-311.pyc,,
cartesia/tts/types/__pycache__/model_speed.cpython-311.pyc,,
cartesia/tts/types/__pycache__/mp_3_output_format.cpython-311.pyc,,
cartesia/tts/types/__pycache__/natural_specifier.cpython-311.pyc,,
cartesia/tts/types/__pycache__/numerical_specifier.cpython-311.pyc,,
cartesia/tts/types/__pycache__/output_format.cpython-311.pyc,,
cartesia/tts/types/__pycache__/phoneme_timestamps.cpython-311.pyc,,
cartesia/tts/types/__pycache__/raw_encoding.cpython-311.pyc,,
cartesia/tts/types/__pycache__/raw_output_format.cpython-311.pyc,,
cartesia/tts/types/__pycache__/speed.cpython-311.pyc,,
cartesia/tts/types/__pycache__/sse_output_format.cpython-311.pyc,,
cartesia/tts/types/__pycache__/supported_language.cpython-311.pyc,,
cartesia/tts/types/__pycache__/tts_request.cpython-311.pyc,,
cartesia/tts/types/__pycache__/tts_request_embedding_specifier.cpython-311.pyc,,
cartesia/tts/types/__pycache__/tts_request_id_specifier.cpython-311.pyc,,
cartesia/tts/types/__pycache__/tts_request_voice_specifier.cpython-311.pyc,,
cartesia/tts/types/__pycache__/ttssse_request.cpython-311.pyc,,
cartesia/tts/types/__pycache__/wav_output_format.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_base_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_chunk_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_done_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_error_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_flush_done_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_phoneme_timestamps_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_raw_output_format.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_request.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_stream_options.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_timestamps_response.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_tts_output.cpython-311.pyc,,
cartesia/tts/types/__pycache__/web_socket_tts_request.cpython-311.pyc,,
cartesia/tts/types/__pycache__/word_timestamps.cpython-311.pyc,,
cartesia/tts/types/cancel_context_request.py,sha256=zInhk3qRZsSc0F1aYJ-Q5BHJsosTrb22IJWhzue-eKE,856
cartesia/tts/types/context_id.py,sha256=UCEtq5xFGOeBCECcY6Y-gYVe_Peg1hFhH9YYOkpApQg,81
cartesia/tts/types/controls.py,sha256=H4CSu79mM1Ld4NZx_5uXw3EwRzTEMQRxKBRvFpcFb8Y,644
cartesia/tts/types/emotion.py,sha256=zocyDcHTiFFnNRgo2YLMi70iGyffa080B4mkg9lcqVc,764
cartesia/tts/types/flush_id.py,sha256=HCIKo9o8d7YWKtaSNU3TEvfUVBju93ckGQy01Z9wLcE,79
cartesia/tts/types/generation_request.py,sha256=ZGVXmHZLaZg7kEg1cVGXLpr8uB3btr2eZt0NEJRZnSU,3582
cartesia/tts/types/model_speed.py,sha256=iiTj8V0piFCX2FZh5B8EkgRhZDlj4z3VFcQhp66e7y8,160
cartesia/tts/types/mp_3_output_format.py,sha256=0WGblkuDUL7pZO1aRuQ_mU2Z5gN9xIabRfRKkjtzms8,731
cartesia/tts/types/natural_specifier.py,sha256=K526P1RRuBGy80hyd_tX8tohPrE8DR9EgTCxS5wce0o,188
cartesia/tts/types/numerical_specifier.py,sha256=tJpIskWO545luCKMFM9JlVc7VVhBhSvqL1qurhzL9cI,92
cartesia/tts/types/output_format.py,sha256=bi9iZVQKmddTw6RjNKG9XAVrgEB7JVNsBS_emFLlGLs,1736
cartesia/tts/types/phoneme_timestamps.py,sha256=SrhPmE7-1-bCVi4qCgMU7QR9ezkwUfqsWfZ2PchzwN0,637
cartesia/tts/types/raw_encoding.py,sha256=eyc2goiYOTxWcuKHAgYZ2SrnfePW22Fbmc-5fGPlV2Y,186
cartesia/tts/types/raw_output_format.py,sha256=jZGVaS0KIi9mU6trfskgA3HbMKJolhrwICnuDhF01ic,673
cartesia/tts/types/speed.py,sha256=4c5WdxocBw6WSMnundSaNnceUeooU0vikhy00FW6M-w,239
cartesia/tts/types/sse_output_format.py,sha256=tRb4VcYqoPJMDyjfTZMCRTblT2NjwIsQhy1oMjxQWW0,676
cartesia/tts/types/supported_language.py,sha256=riDRduThMbMWAq9i2uCfxhwVTpgaFwNDZ9LhEIl4zHY,237
cartesia/tts/types/tts_request.py,sha256=FGcxW-siiQpEzJZSHMET3nDSYHSzRt3WSTO-cCEz9u4,1376
cartesia/tts/types/tts_request_embedding_specifier.py,sha256=eL_qCEr4pvWfy4qp9hZBuVdCincX5DBVqfv1vLt2_Vk,942
cartesia/tts/types/tts_request_id_specifier.py,sha256=ktGdkkTRQ9scA-lt8qJ2jn_E5WzoOK8AXMrVqi71gf0,906
cartesia/tts/types/tts_request_voice_specifier.py,sha256=p-3UQ62uFL1SgbX73Ex1D_V73Ef0wmT1ApOt1iLZmwE,307
cartesia/tts/types/ttssse_request.py,sha256=6KvDQYzetwbFOVvkMWDj94Biz08EZaiX6V1lChsy49U,2423
cartesia/tts/types/wav_output_format.py,sha256=OTAgVn_gBMk252XO12kiNI9lKrbw3n38aBAiqlG5mdU,531
cartesia/tts/types/web_socket_base_response.py,sha256=MWoTt1rGRqUQ8BOad1Zk2SA-i0E8a3JwPLSiehIbFj4,672
cartesia/tts/types/web_socket_chunk_response.py,sha256=VOPXAlyGFdnfC69KxqDWDo1PPMydvQKmAypoWfbW8_s,593
cartesia/tts/types/web_socket_done_response.py,sha256=zZ6V-_pKNifdyuuRHGlZe6Zbc-ZRk-uHk5zgHkZcBEw,556
cartesia/tts/types/web_socket_error_response.py,sha256=Jm26GnK0axyLQI3-JLHC0buYVIU8gKWxLAJlzo-cJFQ,573
cartesia/tts/types/web_socket_flush_done_response.py,sha256=JLiVPDftr1arl_Kvj6038yj0mnjq6x0ooihsbdXajfw,635
cartesia/tts/types/web_socket_phoneme_timestamps_response.py,sha256=R1-Z_W3XF7L7rrPwEOK_EfXHT4FWRpSAX3g71WebM90,686
cartesia/tts/types/web_socket_raw_output_format.py,sha256=9PiOVmPDfT32IDIsmU7UY_rTLOShMMEw1pNv2yZ9Kyg,685
cartesia/tts/types/web_socket_request.py,sha256=_xoAShkCCNTVAWKCvHw5k0Wisq60y4fOWYjG7SA8edM,260
cartesia/tts/types/web_socket_response.py,sha256=fUQbJ6yFzZbzUZPuQWgkFdzP8-FMiKTcya-DIPWjimY,3777
cartesia/tts/types/web_socket_stream_options.py,sha256=MhDSxBFqMuQeWjoyPqXVnTEzLjF8g6aojeigb5dQUgU,596
cartesia/tts/types/web_socket_timestamps_response.py,sha256=kuWXI82ncF1QapnaHEjwrL84qWob7ByQU-yh1e0IEmk,667
cartesia/tts/types/web_socket_tts_output.py,sha256=uvkv0smTBhdm18Rl17C0Ml4Inh79YBHNzAcKnZBs14Y,979
cartesia/tts/types/web_socket_tts_request.py,sha256=Gx8kSINX__VhQ3In3R1-4fq0bfjaMe7iL-M8nDNt0fQ,2150
cartesia/tts/types/word_timestamps.py,sha256=XZ2Q0prdb3F9c3AiOKXu4s3A3jBxE-qIt1npHOf16R0,631
cartesia/tts/utils/__pycache__/constants.cpython-311.pyc,,
cartesia/tts/utils/__pycache__/tts.cpython-311.pyc,,
cartesia/tts/utils/__pycache__/types.cpython-311.pyc,,
cartesia/tts/utils/constants.py,sha256=1CHa5flJf8--L_eYyOyOiWJNZ-Q81ufHZxDbJs8xYSk,418
cartesia/tts/utils/tts.py,sha256=u7PgPxlJs6fcQTfr-jqAvBCAaK3JWLhF5QF4s-PwoMo,2093
cartesia/tts/utils/types.py,sha256=DtsiRwrYypXScLu71gNyprUiELuR1l_-ikVaj47gpg4,2047
cartesia/version.py,sha256=xk5z2FYkgnvzyjqzmRg67rYl8fnCeHEjPpVmD08bjyE,75
cartesia/voice_changer/__init__.py,sha256=UKA8CSAwUb41OL-dcWWUhIsKLLsyY_NQtrklPAVWf9E,685
cartesia/voice_changer/__pycache__/__init__.cpython-311.pyc,,
cartesia/voice_changer/__pycache__/client.cpython-311.pyc,,
cartesia/voice_changer/client.py,sha256=w3Z3A-92Fu5k9NRrfdn7Gu2nqmOONL-xLCHknZhkANY,13509
cartesia/voice_changer/requests/__init__.py,sha256=MRwSUqio3mg_tvfcpAS0wIZ69HvJsc2kYJ0tUBaJ53U,390
cartesia/voice_changer/requests/__pycache__/__init__.cpython-311.pyc,,
cartesia/voice_changer/requests/__pycache__/streaming_response.cpython-311.pyc,,
cartesia/voice_changer/requests/streaming_response.py,sha256=lbo7CJeuh0f5hXT4lKG_sDUZDLJWaLqxcwCuSf1IbMQ,982
cartesia/voice_changer/types/__init__.py,sha256=qAiHsdRpnFeS0lBkYp_NRrhSJiRXCg5-uFibqDWzYVU,430
cartesia/voice_changer/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/voice_changer/types/__pycache__/output_format_container.cpython-311.pyc,,
cartesia/voice_changer/types/__pycache__/streaming_response.cpython-311.pyc,,
cartesia/voice_changer/types/output_format_container.py,sha256=RqLDELdgeOjYqNTJX1Le62qjiFiJGxf0cYnol88-LLM,166
cartesia/voice_changer/types/streaming_response.py,sha256=gH-2-rlpeI3y9Ou0c7AopHUm3Z5uB3HaoPM1RvFCKwg,1875
cartesia/voices/__init__.py,sha256=2D58Bir45LvcvP08QMnPlFE8DD8BONTjPLkIDdKs7vg,1891
cartesia/voices/__pycache__/__init__.cpython-311.pyc,,
cartesia/voices/__pycache__/client.cpython-311.pyc,,
cartesia/voices/client.py,sha256=iEglXf5LlvVlTVMItjE3k9-rcQwrem6jx3GZgsblBpA,38981
cartesia/voices/requests/__init__.py,sha256=XiBJbSYeQCgFMtwywKvQ0Nmp7Zf_0WskzRhgr9c8h38,1072
cartesia/voices/requests/__pycache__/__init__.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/create_voice_request.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/embedding_response.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/embedding_specifier.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/get_voices_response.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/id_specifier.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/localize_dialect.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/localize_voice_request.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/mix_voice_specifier.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/mix_voices_request.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/update_voice_request.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/voice.cpython-311.pyc,,
cartesia/voices/requests/__pycache__/voice_metadata.cpython-311.pyc,,
cartesia/voices/requests/create_voice_request.py,sha256=r6dKb9ga0ZsAi_6PXuE43u2lLgfQg2DIYjk2Neng7pI,617
cartesia/voices/requests/embedding_response.py,sha256=PGZkBD8UBcv2MYQbBXyD4T6lzaE9oSGGwXx-MoXCp0M,228
cartesia/voices/requests/embedding_specifier.py,sha256=PAHdGsVmLLeJC2b1fWHWI_OlhogO1WnJdzoX9pj5N8c,282
cartesia/voices/requests/get_voices_response.py,sha256=g-ZCaCaLOlZSitcKVhdCtfdKQQz8N3W6E7_wZUNOi5M,747
cartesia/voices/requests/id_specifier.py,sha256=UTtoXBEEYaGvg-Dn2QxUDACNB3Vm1O1XbrPtBA3rGzU,252
cartesia/voices/requests/localize_dialect.py,sha256=OHAInU6IP0LBzIY3VYSiU9bRLjXfr1pGXunsLgv1QHs,497
cartesia/voices/requests/localize_voice_request.py,sha256=oh828eqYkiticD_lerc8WemN3bW13mLZpfRDiKbG75g,703
cartesia/voices/requests/mix_voice_specifier.py,sha256=YjOJ2Qt3nqMQzHsYbF1DnZgmZS9zZepLXpji6V9mfgs,266
cartesia/voices/requests/mix_voices_request.py,sha256=6JCzFmWKIS1_t-uSoO1m-FQbLWB1zaykTcGV-1s-RqM,275
cartesia/voices/requests/update_voice_request.py,sha256=XxJ6TKO4M2s1kXQAZRj8uA4okIABvmWiFhAHJv4BS0Q,282
cartesia/voices/requests/voice.py,sha256=M-4lf4W57fx84_JFOy55b9mWcqO4LfzpY-G_Ekv-2Bo,1031
cartesia/voices/requests/voice_metadata.py,sha256=S0jPQtBpEb2WSnYDLQTS7pcbNJpc0d01uWravHaqzso,697
cartesia/voices/types/__init__.py,sha256=yjxMWjoBpwAZ5UJ2iRSC_kKgZvGmqVd09kQxgcTnMac,1782
cartesia/voices/types/__pycache__/__init__.cpython-311.pyc,,
cartesia/voices/types/__pycache__/base_voice_id.cpython-311.pyc,,
cartesia/voices/types/__pycache__/clone_mode.cpython-311.pyc,,
cartesia/voices/types/__pycache__/create_voice_request.cpython-311.pyc,,
cartesia/voices/types/__pycache__/embedding_response.cpython-311.pyc,,
cartesia/voices/types/__pycache__/embedding_specifier.cpython-311.pyc,,
cartesia/voices/types/__pycache__/gender.cpython-311.pyc,,
cartesia/voices/types/__pycache__/gender_presentation.cpython-311.pyc,,
cartesia/voices/types/__pycache__/get_voices_response.cpython-311.pyc,,
cartesia/voices/types/__pycache__/id_specifier.cpython-311.pyc,,
cartesia/voices/types/__pycache__/localize_dialect.cpython-311.pyc,,
cartesia/voices/types/__pycache__/localize_english_dialect.cpython-311.pyc,,
cartesia/voices/types/__pycache__/localize_french_dialect.cpython-311.pyc,,
cartesia/voices/types/__pycache__/localize_portuguese_dialect.cpython-311.pyc,,
cartesia/voices/types/__pycache__/localize_spanish_dialect.cpython-311.pyc,,
cartesia/voices/types/__pycache__/localize_target_language.cpython-311.pyc,,
cartesia/voices/types/__pycache__/localize_voice_request.cpython-311.pyc,,
cartesia/voices/types/__pycache__/mix_voice_specifier.cpython-311.pyc,,
cartesia/voices/types/__pycache__/mix_voices_request.cpython-311.pyc,,
cartesia/voices/types/__pycache__/update_voice_request.cpython-311.pyc,,
cartesia/voices/types/__pycache__/voice.cpython-311.pyc,,
cartesia/voices/types/__pycache__/voice_expand_options.cpython-311.pyc,,
cartesia/voices/types/__pycache__/voice_id.cpython-311.pyc,,
cartesia/voices/types/__pycache__/voice_metadata.cpython-311.pyc,,
cartesia/voices/types/__pycache__/weight.cpython-311.pyc,,
cartesia/voices/types/base_voice_id.py,sha256=nWRC0rvLpjeMpRbLSmUTPziWo1ZrbPxw22l4gEBWp8Q,118
cartesia/voices/types/clone_mode.py,sha256=3sR6wdxym4xDVsoHppp3-V9mpDwP9F9fDfMUQKG24xw,160
cartesia/voices/types/create_voice_request.py,sha256=_q0d8QojmQrpU-Puzd_YvWmiC7cBp_lrbKmTLuknYqQ,1005
cartesia/voices/types/embedding_response.py,sha256=B7MJ79HIAnxtiP6OT0tt27KBDYTZ3VU0MLuQfb5qVOg,624
cartesia/voices/types/embedding_specifier.py,sha256=cf6JfVnISyrvjWup3oAg-RFdMVRxytem6HLwZgKl3gA,671
cartesia/voices/types/gender.py,sha256=OrbTO__3HVNculvkcb5Pz-Yoa-Xv8N_rNMrFoy2DoaA,148
cartesia/voices/types/gender_presentation.py,sha256=rM8pSurYCSH0AGgLsVpVAPp7uz7TQMM1nPa7-Vus7gw,185
cartesia/voices/types/get_voices_response.py,sha256=c6KMkmJepTUmT7I6tAVOGrPst2kkXxDCXLIf1AnR9NE,1136
cartesia/voices/types/id_specifier.py,sha256=yAY-uc9hRJkHXdsSfRZWkE8ga2Sb-KVipOTSXa8Wmp0,634
cartesia/voices/types/localize_dialect.py,sha256=6JpJKeQvtDjCT2n-5yaGOe3D-4nYqUoYrvcCSE2Zxik,463
cartesia/voices/types/localize_english_dialect.py,sha256=0PjZNjQv5ll2wWZxGveQIYCUGLtGDVELK9FBWFe7SNc,176
cartesia/voices/types/localize_french_dialect.py,sha256=aMhqLi_5goAaSGZguZIFOwQ9Yqh5ApL6gS3cDI315lQ,157
cartesia/voices/types/localize_portuguese_dialect.py,sha256=6dcThK1qWyS3c-W--3Zz7HK5ixS0qslEWrVQmKSrl9E,161
cartesia/voices/types/localize_spanish_dialect.py,sha256=h-H52vk0MBOvJqlzPVPgajfQU6oxpTzHoQAKmSDyaC4,158
cartesia/voices/types/localize_target_language.py,sha256=ttngtFVpMvuWAKQztJu_pCaf7V62DzmNq9zthPCb2LI,242
cartesia/voices/types/localize_voice_request.py,sha256=gvjg292kMgji0L9TNO3VqDS0pHO1vGJUcf0l_vEW_5Y,1098
cartesia/voices/types/mix_voice_specifier.py,sha256=B0FE6UREGk1TxlN0GOPwyCuqJbMkWVUs0EFqiJuQfZ8,236
cartesia/voices/types/mix_voices_request.py,sha256=R_8bmUmE1br4wmfH1Qu6EnL9uC-V1z5BV3_B7u51EOw,641
cartesia/voices/types/update_voice_request.py,sha256=_CEH8nuSZn2qZa9xZlANZXOhJd49XLel3dRy2dfOvr8,716
cartesia/voices/types/voice.py,sha256=DnJbBs2aX4JGZOxm0V4VSTsy1ijw42QKqpaEScL3Lak,1505
cartesia/voices/types/voice_expand_options.py,sha256=e4FroWdlxEE-LXQfT1RWlGHtswl8bmXaLMr3xza1fMk,169
cartesia/voices/types/voice_id.py,sha256=GDoXcRVeIm-V21R4suxG2zqLD3DLYkXE9kgizadzFKo,79
cartesia/voices/types/voice_metadata.py,sha256=4KNGjXMUKm3niv-NvKIFVGtiilpH13heuzKcZYNQxk4,1181
cartesia/voices/types/weight.py,sha256=XqDU7_JItNUb5QykIDqTbELlRYQdbt2SviRgW0w2LKo,80
