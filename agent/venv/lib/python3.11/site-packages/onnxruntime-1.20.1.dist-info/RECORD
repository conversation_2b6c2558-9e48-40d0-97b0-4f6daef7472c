../../../bin/onnxruntime_test,sha256=Wb0WkAhYG1IxwGSOUhIQd1rZB6qVV7R7lZpTM1VCd-E,263
onnxruntime-1.20.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
onnxruntime-1.20.1.dist-info/METADATA,sha256=ouk_C2kP74N1gID6BYT8WN0pHDBmDv9IC3yYSvP_lU0,4531
onnxruntime-1.20.1.dist-info/RECORD,,
onnxruntime-1.20.1.dist-info/WHEEL,sha256=DpbpCRfolfvgUWMFq4N-a-S5LVEsqr8U5lGGgMBU6Jc,115
onnxruntime-1.20.1.dist-info/entry_points.txt,sha256=7qLS4FbGXwPZjfdpVAGpnmk9I6m6H5CxEnwcCx1Imjs,77
onnxruntime-1.20.1.dist-info/top_level.txt,sha256=zk_fJEekrTm9DLxX2LwGegokVqP6blqPhFoMIuh0Nv8,12
onnxruntime/LICENSE,sha256=LwfHJ1Gu2ZeQuKSGnPIxHfhahgsi3tBfoigDWHpIkiw,1073
onnxruntime/Privacy.md,sha256=mhX5_Z1Me238NfI39GGGP4T49zz2GJ4zCBimdNOCiHE,2469
onnxruntime/ThirdPartyNotices.txt,sha256=z3NC97pILvcVrlj19Jeo01ZPolUWQXWuoyTNKTxXAaA,338538
onnxruntime/__init__.py,sha256=mBgYmJLV7vjyCssKmYFx6GAysosH0DjsKMZ0chn1JFs,4454
onnxruntime/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/backend/__init__.py,sha256=kq18WcB0Ol38CSGHL-ONwpkfUE0KcmojOq6k-VGtowY,328
onnxruntime/backend/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/backend/__pycache__/backend.cpython-311.pyc,,
onnxruntime/backend/__pycache__/backend_rep.cpython-311.pyc,,
onnxruntime/backend/backend.py,sha256=jx-shIC_1-VmQFcVYlHpCe_Ok52qutVF4YWPfsmnxTU,7947
onnxruntime/backend/backend_rep.py,sha256=gDHL_2xJ9eSBpV4-NN2tRnGE307URXJF3ctXsauLMyc,1768
onnxruntime/capi/__init__.py,sha256=WSNxt3l4lkWA_0vhw1ED4I7KOO0mnAuHwSgFYqbq0Lg,247
onnxruntime/capi/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/capi/__pycache__/_ld_preload.cpython-311.pyc,,
onnxruntime/capi/__pycache__/_pybind_state.cpython-311.pyc,,
onnxruntime/capi/__pycache__/convert_npz_to_onnx_adapter.cpython-311.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_collect_build_info.cpython-311.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_inference_collection.cpython-311.pyc,,
onnxruntime/capi/__pycache__/onnxruntime_validation.cpython-311.pyc,,
onnxruntime/capi/_ld_preload.py,sha256=_LrnIcTa5OjTvpNBRjbSghdseGllvp7xFtPBBT4q0To,406
onnxruntime/capi/_pybind_state.py,sha256=nwOSruBAJQb9jaG1g5iupqd4lnQ8zoq6sHObmqyePAA,1500
onnxruntime/capi/convert_npz_to_onnx_adapter.py,sha256=sgCOExT-Qkd3QWq6FuiiX2PHkWAk6cpPVCszIy4geUA,1533
onnxruntime/capi/libonnxruntime.1.20.1.dylib,sha256=9xP3_dQgH-5cKGfrMIumejaQT89AmpO8ut-Dffg4aqE,54116728
onnxruntime/capi/onnxruntime_collect_build_info.py,sha256=j9LpBEiYOXefX_PkIun_E1WsbW2PyrINMkwbGnvqOYY,2062
onnxruntime/capi/onnxruntime_inference_collection.py,sha256=RjJu9WO7lt12W0ydTzaoT5tEW8dOLbrLWbUC34ysPp0,44663
onnxruntime/capi/onnxruntime_pybind11_state.so,sha256=2tgiOX7nHKSChVU6--_7clUqI4OrsP8_vrqTFbvzrEM,57216602
onnxruntime/capi/onnxruntime_validation.py,sha256=utvNDOfnALjoWqxA3kNCUOqqscTikObVb8tdH3_49Yc,6455
onnxruntime/datasets/__init__.py,sha256=c5m9UfRXSJZb7tiLxkHsPI2cFyPjAOXAdYEC9jVMXpQ,454
onnxruntime/datasets/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/datasets/logreg_iris.onnx,sha256=giR4TJjXNBLZ_ZmrzVejhWi9WQmA0PvlkWRkUxxS6Pw,670
onnxruntime/datasets/mul_1.onnx,sha256=cfQxxOkyHsb76xWNAu0kBFmn3MmGc_p5pPQ5zkLvrxA,130
onnxruntime/datasets/sigmoid.onnx,sha256=U0Crpnp-NHUWKteUN4r1XxcY9V-aXXS0r2Dsx_emJLY,103
onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py,sha256=_sxYZW7jGxHTNkVq0-bl_OToq2DdMXGOnpaHVhgN2Ms,2172
onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py,sha256=zjA-WFd_IDkYlI-PmAQAmfRCeN4txTIxN7AdoqIFW6I,2575
onnxruntime/quantization/CalTableFlatBuffers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/KeyValue.cpython-311.pyc,,
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/TrtTable.cpython-311.pyc,,
onnxruntime/quantization/CalTableFlatBuffers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/__init__.py,sha256=RpFe2IYo3gUZjhfVQ7rLGVM1QGOMfOvrnTZWeGvGub4,721
onnxruntime/quantization/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/base_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/calibrate.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/matmul_4bits_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/matmul_bnb4_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/onnx_model.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/onnx_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/preprocess.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/qdq_loss_debug.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/qdq_quantizer.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/quant_utils.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/quantize.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/registry.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/shape_inference.cpython-311.pyc,,
onnxruntime/quantization/__pycache__/tensor_quant_overrides.cpython-311.pyc,,
onnxruntime/quantization/base_quantizer.py,sha256=AzcECZZC7VjlHjF-heZGz7miUdzw49bsf80jUUrLyCs,27271
onnxruntime/quantization/calibrate.py,sha256=FthP343p2Nv2O0f1SOB_k3bU3b-dED6ZfNeQYoBI-n0,52656
onnxruntime/quantization/execution_providers/qnn/__init__.py,sha256=maE-crjmApBM-sGIuY_PzjhlQusQr8D-FAhA9eqm91g,118
onnxruntime/quantization/execution_providers/qnn/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/fusion_lpnorm.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/mixed_precision_overrides_utils.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/preprocess.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/__pycache__/quant_config.cpython-311.pyc,,
onnxruntime/quantization/execution_providers/qnn/fusion_lpnorm.py,sha256=Z2fbMv852jzCyWkfTTasq5CIPSvtD-EWGmUH1nfHz18,5195
onnxruntime/quantization/execution_providers/qnn/mixed_precision_overrides_utils.py,sha256=_svvD72k5zjNDFDRv_UTdktpdMXcjgwwDwpzJLQJae8,18587
onnxruntime/quantization/execution_providers/qnn/preprocess.py,sha256=-mIYu2fcnM3MFLhUKjIf--yiWnTV1xG_s4nAKLi5sB8,13885
onnxruntime/quantization/execution_providers/qnn/quant_config.py,sha256=LZPgmRFrYZ1qrV7Gm3rYzJCMhUy1LXph_4CigQHhfpk,18301
onnxruntime/quantization/fusions/__init__.py,sha256=3zBtHrLlvFGpt1LdNopDBmhwFvX5JH1XCrlnq93czx0,160
onnxruntime/quantization/fusions/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion.cpython-311.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion_gelu.cpython-311.pyc,,
onnxruntime/quantization/fusions/__pycache__/fusion_layernorm.cpython-311.pyc,,
onnxruntime/quantization/fusions/fusion.py,sha256=8vTLAHBJQqgUPwK2U5yd6UwUtk1MHPyKzlLj-4lXQRI,11777
onnxruntime/quantization/fusions/fusion_gelu.py,sha256=oiFqtOUcumkPZgH5bdSqmteomM6WEaewwM18wpv3myw,10375
onnxruntime/quantization/fusions/fusion_layernorm.py,sha256=E4Gfyi-1MOdB5A16JWxQQQNYnklckL2hCRdNp0OyeuQ,5171
onnxruntime/quantization/matmul_4bits_quantizer.py,sha256=4kPt4LD56RXy3SmFE45oirzIYA0OIsqVmKzvG9Z27tk,60116
onnxruntime/quantization/matmul_bnb4_quantizer.py,sha256=GK9mFeVmmgzXdShhDEPHjUt9WTLC4Aq44sKvgHIy9Ds,9060
onnxruntime/quantization/onnx_model.py,sha256=Yl9zm9U3SMOK0CqYcePVeyUFdcowtM76E81Jjl3Ane4,23940
onnxruntime/quantization/onnx_quantizer.py,sha256=WDF43i7hc14xtBsvZs72O2vp5LU8qSbZU7c2x28ORVQ,42934
onnxruntime/quantization/operators/__init__.py,sha256=e1MWFAWOJ_Fnj76lc8LnG22v2Kwi_yNvKh0aqkidEKU,83
onnxruntime/quantization/operators/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/activation.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/argmax.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/attention.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/base_operator.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/binary_op.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/concat.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/conv.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/direct_q8.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/embed_layernorm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/gather.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/gavgpool.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/gemm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/lstm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/matmul.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/maxpool.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/norm.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/pad.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/pooling.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/qdq_base_operator.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/resize.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/softmax.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/split.cpython-311.pyc,,
onnxruntime/quantization/operators/__pycache__/where.cpython-311.pyc,,
onnxruntime/quantization/operators/activation.py,sha256=Y9gARZLgf-3IR9pCaxNeVBoGu0ZYiQY8ZG4Kctw4Kf8,4426
onnxruntime/quantization/operators/argmax.py,sha256=hrO0DIgI3zwzlW6McgfSWz97bUvHuWGTsyH2uhG1QNE,571
onnxruntime/quantization/operators/attention.py,sha256=RbfUlkFtLP1O5wLlwv5H8YolNejf0XyuB4RfqGKVleM,2564
onnxruntime/quantization/operators/base_operator.py,sha256=dKKc9w0qLnr9HOtKTG8iJFKZPwNwd5Vldu-1RJmPyvk,1092
onnxruntime/quantization/operators/binary_op.py,sha256=QHyza_ubCH6KLKp7JtWGoNLYBOLVxhuCh1crmIYL9Mo,2472
onnxruntime/quantization/operators/concat.py,sha256=cx7Gn8jh4foBbopRb4JPAoNsSyP1AKc3_gGWnVyJE0Q,2081
onnxruntime/quantization/operators/conv.py,sha256=m_UbOggkcbZIvtn_zC_J3lCcjVItfTHkCRwZhzDp-kw,9910
onnxruntime/quantization/operators/direct_q8.py,sha256=7TUU1-JAm5Fu__4RqRxGPgXwqqD3_0cuS0SUR4iqKNU,3311
onnxruntime/quantization/operators/embed_layernorm.py,sha256=aGq2yw4ZtjvgC5_teAI_fAn18UEgr1ShJrXT7t18JGQ,3937
onnxruntime/quantization/operators/gather.py,sha256=LKwt6GvMm80ycEdYDPc7yODs7hpSuDLieSib12lbm3o,2166
onnxruntime/quantization/operators/gavgpool.py,sha256=zbLZUxYq94tx-OVTZWv64AW-y8Mvmk-2cXQNbKM1KJY,2383
onnxruntime/quantization/operators/gemm.py,sha256=2ZVcgJ8iHOGSpfqyXx37RgPC1nJuAzDPEWlMUo5hg_k,6086
onnxruntime/quantization/operators/lstm.py,sha256=0z6NQCPjUc0EjuvIjjQPydvrVOZz1L-otPgyf9r4kmw,5067
onnxruntime/quantization/operators/matmul.py,sha256=Dy0zS93amR6Ptl2IDN0IYqeoiZguJe2xAE6_edONPb4,8268
onnxruntime/quantization/operators/maxpool.py,sha256=6F2D46x-7sL5bXPqTq4OjeK8HcpsogvdXNCIPTAmEek,927
onnxruntime/quantization/operators/norm.py,sha256=DaLEPcVnfkwaD6epTgrC7heE-DjiWBTs2osASMYT90E,1609
onnxruntime/quantization/operators/pad.py,sha256=ku4R1t-VgBER80VoXrEy_4xBdUo97DY9esDfmZ1zawE,7779
onnxruntime/quantization/operators/pooling.py,sha256=ZTI8FjLwk-eao7-qY45cCsUtoblZVvJ7PIZ0W9YpQEw,2218
onnxruntime/quantization/operators/qdq_base_operator.py,sha256=gzpVMYW9pZiWRgbZEHpKgGuWqcOiMssYth-Tpyad2sg,801
onnxruntime/quantization/operators/resize.py,sha256=XKpGZ9r7Yk9DOYLAQRc_mKy-jviE83WnT4FV65CLTLQ,928
onnxruntime/quantization/operators/softmax.py,sha256=N32Qyc9EXrYsTIT9JOAJgEZi-Dh0D4FVHFSsmOImvR0,2640
onnxruntime/quantization/operators/split.py,sha256=HXZ0LK4T8vsRbhyHI8DjBxCmJ2nA3zISWM9Ia9ylAhI,2195
onnxruntime/quantization/operators/where.py,sha256=tDr8XSGQE6VuQ2U_miRNsOiw7-6XZkeO96dM1naX4eg,3040
onnxruntime/quantization/preprocess.py,sha256=EF1WpxfMOoGSJZbB04BL9K4-mAHY7xWj5thvz1t64dc,4904
onnxruntime/quantization/qdq_loss_debug.py,sha256=BYUOH6HNpmikuH_S4Ki7IZ_FGgSIVQVP_sQSkHhdH1o,15498
onnxruntime/quantization/qdq_quantizer.py,sha256=LUAZqphKJlo73JGRFX03HGiG20psxmLT8NSxmAGSDe4,67035
onnxruntime/quantization/quant_utils.py,sha256=KY8anGZuplKymr6mHaNemRsaFeK-vTcI-XcOluSfTms,38920
onnxruntime/quantization/quantize.py,sha256=11a6GGfNd5BCzkvAZqaip0j-vBBj3uuI-1zBibuJB3c,51108
onnxruntime/quantization/registry.py,sha256=QxzS3A0DTmOWJk2vabHMA4jGmq5jlBeG2ZBsDBlUewc,3686
onnxruntime/quantization/shape_inference.py,sha256=330AEEuLqDFGHR-vvJyV-b5jotiatJ3lOrfY-09W03Y,8524
onnxruntime/quantization/tensor_quant_overrides.py,sha256=ZmPXZMnw_24JedppqaNPqR0VuH5kDhHX0QwTq8DWUMo,20767
onnxruntime/tools/__init__.py,sha256=ix4eytbWVV8QKeZu-NVPjb8oIMWrSE0mKzfVhn7chBk,518
onnxruntime/tools/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/__pycache__/check_onnx_model_mobile_usability.cpython-311.pyc,,
onnxruntime/tools/__pycache__/convert_onnx_models_to_ort.cpython-311.pyc,,
onnxruntime/tools/__pycache__/file_utils.cpython-311.pyc,,
onnxruntime/tools/__pycache__/logger.cpython-311.pyc,,
onnxruntime/tools/__pycache__/make_dynamic_shape_fixed.cpython-311.pyc,,
onnxruntime/tools/__pycache__/offline_tuning.cpython-311.pyc,,
onnxruntime/tools/__pycache__/onnx_model_utils.cpython-311.pyc,,
onnxruntime/tools/__pycache__/onnx_randomizer.cpython-311.pyc,,
onnxruntime/tools/__pycache__/onnxruntime_test.cpython-311.pyc,,
onnxruntime/tools/__pycache__/optimize_onnx_model.cpython-311.pyc,,
onnxruntime/tools/__pycache__/pytorch_export_contrib_ops.cpython-311.pyc,,
onnxruntime/tools/__pycache__/pytorch_export_helpers.cpython-311.pyc,,
onnxruntime/tools/__pycache__/reduced_build_config_parser.cpython-311.pyc,,
onnxruntime/tools/__pycache__/symbolic_shape_infer.cpython-311.pyc,,
onnxruntime/tools/__pycache__/update_onnx_opset.cpython-311.pyc,,
onnxruntime/tools/check_onnx_model_mobile_usability.py,sha256=8WMkAxFkHa2dNMwDP94RxxG04xSgG_wRv-cQ-IIpuzk,1670
onnxruntime/tools/convert_onnx_models_to_ort.py,sha256=C8wYVLwW55m7L_Im_zSIdFBPOAZavk2mS1snR4kMQMw,16433
onnxruntime/tools/file_utils.py,sha256=O01Y8jzKnv3u_y193b2e9L_1248SLwDyqKa9vYuAqxA,1523
onnxruntime/tools/logger.py,sha256=6dQahm8pgIqrgH1nlWnCVGBIhG6JjtldKv9fdnaJKmc,322
onnxruntime/tools/make_dynamic_shape_fixed.py,sha256=a85YO7eQtB7KMMgVkqMGAGMODSKoagYvDxpxDAxiP-c,2536
onnxruntime/tools/mobile_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/mobile_helpers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/mobile_helpers/__pycache__/usability_checker.cpython-311.pyc,,
onnxruntime/tools/mobile_helpers/coreml_supported_mlprogram_ops.md,sha256=2QIM7eYTii0mt6uH9SXPu6cTnN9IaWiQpCLPcpYXWIg,2006
onnxruntime/tools/mobile_helpers/coreml_supported_neuralnetwork_ops.md,sha256=fBYJaapi0najonpmPDkJBRLXyDXbTYeIaRKMhSes83Y,1915
onnxruntime/tools/mobile_helpers/nnapi_supported_ops.md,sha256=5Mo_h-tTDzV8y1zO1qObiBbn4AukC_9L0YEmChFUOCU,2327
onnxruntime/tools/mobile_helpers/usability_checker.py,sha256=iV7c4Shwt9LxvIcndTowrdZuID4vNP3VgWYw_TK9U8s,31638
onnxruntime/tools/offline_tuning.py,sha256=yKq99nKjV8S1J2Z47NQPR_xhN3ax1KlePG4Er-K-ccs,6211
onnxruntime/tools/onnx_model_utils.py,sha256=XwsO0z38aS65cyYTQfET7dZJOZ7z6HXXkZ8pCSTvVkU,16279
onnxruntime/tools/onnx_randomizer.py,sha256=TrcFpXWQQmoTVRiEwyW04eN9Em2HlQCZfdOvZW9x54o,3276
onnxruntime/tools/onnxruntime_test.py,sha256=NKbMIy6iof9VFhlOdYiOzNMajfcAq0qXByWXKCOPucs,5606
onnxruntime/tools/optimize_onnx_model.py,sha256=Id5EnsBHf4pwl54gHfNV1S1wswpO9SiFYjuIia9tyOM,1914
onnxruntime/tools/ort_format_model/__init__.py,sha256=2Pvm73ZSUlYyX5E4MO8EvZlGUqkGVMO4-iZqqqHkxhA,1353
onnxruntime/tools/ort_format_model/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/operator_type_usage_processors.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/ort_model_processor.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/types.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/__pycache__/utils.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/operator_type_usage_processors.py,sha256=84i7lHZaMGDwSmTIV6x49uA3Q6GTsKG7GLjZHLH5aas,26548
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py,sha256=qI3h9M5be1RdN9xjtPZfPA8f1nYP9fTnRONBHVZcnLc,140
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py,sha256=vn12qdmUR0HU0HHERo5f5UL4HWkfXPW6oxeHjctSUxk,2026
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py,sha256=ARUtfrRix6SScnGHYCrkhGZ32480ldNWfxeQBOF-35Q,10850
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py,sha256=nfSjO80-abBlnQOt0L-vdyuCqISUhyrW_Y6w1qcezRY,328
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Checkpoint.py,sha256=CXq7KXLKFy7aiN6MyeQrkdtAmbpOxf1u08LXwvnq0Ow,4217
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py,sha256=EVLQNjNrIUqGmW_eSqjyYjXSTNQd0MDRLS9DxbkLM1E,4528
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py,sha256=9vhCLfDGnVaDC6YB-bIPa6pLBMrkcB7xcrMisEezsS4,2458
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py,sha256=55kBQBL7594VEfbtmcry8MJj8pUOi4g2AmqOnqg2akk,3582
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py,sha256=z4TTKo6QbQernBJbry0l_9G9Ko1FjfZ3Q3JVwqZyuxc,2610
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py,sha256=frSBCWZFKPfirFlFCzKnF3Xs2MkO6t6_yb0R5q-j2eM,2191
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py,sha256=yJ0IX2yfN1Ia8dyd7N5nrCHlaKf1bcuihMDqCX3tZWI,2494
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py,sha256=MAFjoNc7461PlCCa3d1N6Mx0-unsm_lzxk3brQlwAZ8,166
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py,sha256=cWuMNA9Y6VbVmQhkyIFD9N7NNPOsDbMrZP7VuKgZ7LQ,1105
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/FloatProperty.py,sha256=MiGr-O8Lx0ToQL_kmLu8do66bB-BdNM8IKvqdwhdgns,2008
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py,sha256=wTBSdIR0VWk6kLlEVojEA_1wdo_jbg_6yCd9Hy6OvHQ,10719
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py,sha256=6rR39u9d_gU4RdlLe39L0uzl-5ZAmeChIn81Zd-GT-M,3037
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/IntProperty.py,sha256=kfBNCcT7HUEPt9TZfIcRhgd3yfR_GrDjsFl66FHU8Qo,1970
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py,sha256=kezd9lWvFPbWMjy9Lj4OMW1vcrotZGo4LDB60-9hGAo,3102
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py,sha256=hB0Rg92ZZorvUoe2M1stagu9tKtHviAUWHNskvqqItA,2789
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py,sha256=-RmpuZRXMmiC2FpiV260gLtQb6Jh9qcBXyKlp3bEbog,2123
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py,sha256=3IFVnxjo8_1MkQ6PWOswW88d-CIQNmN66NfkDkra-Ow,7440
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ModuleState.py,sha256=Xm7rSMW-SJPHEWLoYB7g_FnP95nn1i1B7KS9VdbF604,4853
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py,sha256=IKXdsA-4SlHyOTODetve-V_tYe09LRB6cXb4K1_7PEg,10401
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py,sha256=2YK97cUPr3FydQ1RulDAo4qJMAepZA3zQbXlD59k4lM,4057
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py,sha256=8eNAZzaWvEJ9cIS71joG9Wo6uYR-GTVwfSw7mfVF150,144
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py,sha256=kStUDjt223p_akD6bqQ2hD0JE4IFJHtyDbzMlIdkmCs,5984
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py,sha256=ROhU01ximH-wwwJl5y5TkFjAb5TRyNKbWEDkA41mM4E,3296
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py,sha256=B0d14jsA-CFXbq2TJixFatNPYbaMfMa9rN36c2iO8SU,2032
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OptimizerGroup.py,sha256=9nEe2G1ps352MEEJbqJ8VDCCYI8uDyAWbIQZLnrfnEM,4018
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ParameterOptimizerState.py,sha256=jaz70JW_AZrdvJo9g_RlizhlKD2BwGGQUozlljOYcrI,3127
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/PropertyBag.py,sha256=GwjF-q36uYav09hWQdJh5uoi7cXrblu4L1Sebx3NHO8,4947
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py,sha256=k4NYCi_BZg_Ksp1HWQnIZ9FN2oz8Knx8YRGZh3P3m0E,3962
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py,sha256=0sD-EEVnk_cKGcMcSUFUZPzTJ5NwPKCNYMvQLMHm1Zc,3741
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py,sha256=GLlnP6QRdxad_jyKbE_3m4UVe9OAwH3Gz3RO427wKFg,2721
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py,sha256=4CwMuqU4DYbDSRhrp27_rSuWRjjDBzjDv0e1S7f3e7A,1771
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py,sha256=6m4IOPr4DO7OyhAS1hbMVwEErHB3RC95gTNHRhdMb3I,2274
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py,sha256=eLVpVpHP2sjTRJf03L6X05lKJz75LJedeCIgWqz3upE,3692
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringProperty.py,sha256=d9gKJIJrV7QSh13Cs3Zpmy5T_dPtmqpmgullT_I9erc,2043
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py,sha256=5IQVl23MFYv6T42nKGvJKlTx5y7xm09aQZ2S6LHj04w,2080
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py,sha256=Gz0L-aww4OKtgaK6-tzxcFRDq5-gzwNfBTYDbM8g5eI,6599
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py,sha256=BSNquneILrL8MuMoAjxcQrL9vg9WK4tO5NxugCOGPb8,474
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py,sha256=lNQjppdw2Lf5O2R3ic1jS1TqK1cSNCpoLTeq9VuZL9I,2255
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py,sha256=I074s78FFXn92Le0S6DkfSulSy_HGfvBvUol_WmOlEQ,2516
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py,sha256=d0nu7EsL1dtHXGVWosQIAdpUYzliBOkP1iyzmCdm4q0,189
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py,sha256=7-yZBW15RCG_2qgKTGfmFUxkDYQx1364GCJIZw60r4o,2571
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py,sha256=6kmf2M_iyeXczT1oVap44dHuO31sUhS-wT9xnQKqKZI,245
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgTypeAndIndex.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Attribute.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/AttributeType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Checkpoint.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedKernelCreateInfos.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedNodeIndexAndKernelDefHash.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSessionState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSubGraphSessionState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Dimension.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValue.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValueType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/EdgeEnd.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/FloatProperty.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Graph.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/InferenceSession.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/IntProperty.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrArgsEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrResolver.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/MapType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Model.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ModuleState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Node.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeEdge.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodesToOptimizeIndices.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OpIdKernelTypeStrArgsEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OperatorSetId.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OptimizerGroup.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ParameterOptimizerState.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/PropertyBag.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecord.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecordContainerEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizations.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SequenceType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Shape.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SparseTensor.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringProperty.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringStringEntry.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Tensor.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorDataType.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorTypeAndShape.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfo.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfoValue.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ValueInfo.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/ort_format_model/ort_model_processor.py,sha256=bJISrFBV44Lb1gLTqstgMuiUINxzfc98s9ozWcEQKP8,4386
onnxruntime/tools/ort_format_model/types.py,sha256=QkmC1WFztFykG_wp8CJBAhgjTS2us2uKM8qF5ZMyznc,4382
onnxruntime/tools/ort_format_model/utils.py,sha256=crQSHZv3cu3xgXVQmfTx1WS7xHeO0QA_GxH821myGxg,2542
onnxruntime/tools/pytorch_export_contrib_ops.py,sha256=IDjYKIxCo1EgxrPV5Mb2SrEc2LOZDUnOeh5Hyjhx6og,3981
onnxruntime/tools/pytorch_export_helpers.py,sha256=Lc4VdbslfrQ6UtnG7U6ZJhxN_0IG_oHMgWfj4FIUP3U,5840
onnxruntime/tools/qdq_helpers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
onnxruntime/tools/qdq_helpers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/tools/qdq_helpers/__pycache__/optimize_qdq_model.cpython-311.pyc,,
onnxruntime/tools/qdq_helpers/optimize_qdq_model.py,sha256=WLsZifVNVwwkX8Uil4TeA8mfsqtpxUMtb0flRN2gjzA,1242
onnxruntime/tools/reduced_build_config_parser.py,sha256=StUHSU4pW0VU6Euj5GzDXdimhTkV4p582Ia3os40Tsw,9923
onnxruntime/tools/symbolic_shape_infer.py,sha256=mKmhqXgAvSQFKhYzIvx-_1Z1MRBJeXXACFx5tYBCw4s,138980
onnxruntime/tools/update_onnx_opset.py,sha256=nfFF8srjpTyO8SCnHZMt3zdMWB0Q5HB1BgcdZAnc7UI,1151
onnxruntime/transformers/__init__.py,sha256=RS5B5wWnwFKH0KXZeWydhjs9Wl9sPRqgU_lB1_dUYN4,313
onnxruntime/transformers/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/affinity_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/benchmark_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/bert_perf_test.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/bert_test_data.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/compare_bert_results.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/constants.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/convert_generation.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/convert_tf_models_to_pytorch.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/convert_to_packing_mode.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/dynamo_onnx_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/float16.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_clip.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_sam2.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_unet.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_attention_vae.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_bart_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_base.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_bias_add.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_biasgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_biassplitgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_conformer_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_embedlayer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_fastgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gelu_approximation.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gemmfastgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention_megatron.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_gpt_attention_no_past.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_group_norm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_layernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_nhwc_conv.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_options.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_gelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_layernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_qordered_matmul.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_quickgelu.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_reshape.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_rotary_attention.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_shape.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_simplified_layernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_skip_group_norm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_skiplayernorm.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_transpose.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/fusion_utils.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/huggingface_models.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/import_utils.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/io_binding_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/large_model_exporter.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/machine_info.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/metrics.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_exporter.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bart.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert_keras.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_bert_tf.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_clip.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_conformer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_gpt2.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_phi.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_sam2.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_t5.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_tnlr.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_unet.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_model_vae.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/onnx_utils.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/optimizer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/profiler.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/quantize_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/shape_infer_helper.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/shape_optimizer.cpython-311.pyc,,
onnxruntime/transformers/__pycache__/torch_onnx_export_helper.cpython-311.pyc,,
onnxruntime/transformers/affinity_helper.py,sha256=N0QR2jl0z6v4AM15cyKnzWf7T8S5T3ZBweOQ5WE1lx8,1402
onnxruntime/transformers/benchmark.py,sha256=aYZEBqbQ71FcLDtgTOytMsT_IVx4gvVTAlSHTRGyyzM,32797
onnxruntime/transformers/benchmark_helper.py,sha256=lQlXizaTjJOk_CtAXDEVClHH_vIBG5zD_kUh_gXp-k8,22599
onnxruntime/transformers/bert_perf_test.py,sha256=xyFMfH0EhlBo-bJUtcGhS2BVPyAVoBwQ9ZeEOTjGgko,20361
onnxruntime/transformers/bert_test_data.py,sha256=kRMPtQmnZdFBb5vSO9tKHaHCduGTmAtDhuxteY05uqU,22868
onnxruntime/transformers/compare_bert_results.py,sha256=LZWh4XfZe8vuJ6YnXyxDeZDKY1hMKojc2sL98rY0qek,7662
onnxruntime/transformers/constants.py,sha256=0Mjx4eED8GOjjG_p5A8hduNIJCiPqcWqcJWKnZMtWwk,1080
onnxruntime/transformers/convert_generation.py,sha256=YDkA7yHt-X6ziQFQDO_pJ_8DpcGfH9EaG5kA_5j0xrg,124434
onnxruntime/transformers/convert_tf_models_to_pytorch.py,sha256=tYR_wmJABRg9BQG9aj3oO3j0ABSu4yi4_Sg2zx6VeF4,6500
onnxruntime/transformers/convert_to_packing_mode.py,sha256=loH_EtpysBQAweL0Q23XjBlQlXqxpVPNp8fyifk9saM,16467
onnxruntime/transformers/dynamo_onnx_helper.py,sha256=HTZWBkzt1ujQbQ7XyQglqf6hLIsdzJuLi6UzlhpqBmY,3693
onnxruntime/transformers/float16.py,sha256=7pPQ1DS0WJJJ3USjuphj6WSjwcj2RXXXE-W48aXddPY,24190
onnxruntime/transformers/fusion_attention.py,sha256=wgscXlLYeiAVwRUgteWoEqP1i1_HcPs2bFm3S21Mx8w,51608
onnxruntime/transformers/fusion_attention_clip.py,sha256=KBJBsMfUBXUW8fBmFzQRoQjoV5VWFyG4SWfP9Dd8C3c,10267
onnxruntime/transformers/fusion_attention_sam2.py,sha256=nyS16hvt15-PfVEljrdvIqhhBWnaaWVwH0A4BPlcdQg,20812
onnxruntime/transformers/fusion_attention_unet.py,sha256=YkSP1pLLFqPwkTxt3EImx6UPbGz6qRC2Jt0l_kTm0PE,55628
onnxruntime/transformers/fusion_attention_vae.py,sha256=tD-Jpj7lFWrqUocsjg4OseQqpg6fMjWfnRbjoeKkaDM,12117
onnxruntime/transformers/fusion_bart_attention.py,sha256=aIKudwc1pzjKTkO5SHG-2CwnVSwNg9WeddputnK1ua4,28797
onnxruntime/transformers/fusion_base.py,sha256=_1VqOmrgBIRQGrkPJSRO8l70JHKqlyYc43Ny3PXAWLQ,5733
onnxruntime/transformers/fusion_bias_add.py,sha256=MZRh6MEC1-YiR_bxK_22iOWd2onJzFUJ0B3FpsLjbkw,2008
onnxruntime/transformers/fusion_biasgelu.py,sha256=-Z_TsQFO-9RsWLezGXe4U2aJjuX4hVgIGwOEclblJAo,2234
onnxruntime/transformers/fusion_biassplitgelu.py,sha256=IEqZHlxSsG_Jr5Z24_66vAZqdaLgWk-mhkk8LEzue0U,4405
onnxruntime/transformers/fusion_conformer_attention.py,sha256=z1XCVRUGNui3zsn0RlXIGe-q2DuRcebuY4XTj_QooPo,4878
onnxruntime/transformers/fusion_embedlayer.py,sha256=oaVxPbkxbGxkO3gij1wGFBkSOFVZ7zcvXL_bNHp882M,35939
onnxruntime/transformers/fusion_fastgelu.py,sha256=voCs2o1L-I0Ci7Uc3HgRGGUd8M8miZ47gpWPL6SUu6s,12964
onnxruntime/transformers/fusion_gelu.py,sha256=q59o2sP7B8Pn3g3sYMnkrpvUJmCXwDuXzo_Z1e-SbQ0,10236
onnxruntime/transformers/fusion_gelu_approximation.py,sha256=i2xGW-9zl6Xburqq0F8DI87AyqsgnhjLeFW-rBiN9ys,1004
onnxruntime/transformers/fusion_gemmfastgelu.py,sha256=tvooaXI4O4V_wn4KZUCDC-5YV7DREx_NYiMHUw-XVOU,4136
onnxruntime/transformers/fusion_gpt_attention.py,sha256=mk-UKIjouzrtkMl3A-LdLuLc6wlOqxAdbE2gRiYYwpM,21962
onnxruntime/transformers/fusion_gpt_attention_megatron.py,sha256=HbDoDXXYhqy4sEFUJvaf5Ly5U6u6upCg8JUA1d78CQE,13284
onnxruntime/transformers/fusion_gpt_attention_no_past.py,sha256=Dk8l6aT7rl4sg_58yMO-uFNqOdCnA6bBla8d8t4tSKc,10534
onnxruntime/transformers/fusion_group_norm.py,sha256=4bBNcl7LGctPrnsYlXvsw12kcNPagq_dOJX4-SGQnnQ,7425
onnxruntime/transformers/fusion_layernorm.py,sha256=8XchgC-OD63fkaBp-bIwxX7zCYD00KQJu4U0541ijFA,19048
onnxruntime/transformers/fusion_nhwc_conv.py,sha256=hCv8HVBDXo5JRI0vnNlo16Tv0_cLTdRtTaDbyNGYpxo,3873
onnxruntime/transformers/fusion_options.py,sha256=s83h8KkTt8V4_lAQt85YpIlnx44-6gRcuVt0b8vuNo4,12364
onnxruntime/transformers/fusion_qordered_attention.py,sha256=GcAodknP95tKZQJD6aQh7LWByxhg50f7-8xBRkzzvl8,16742
onnxruntime/transformers/fusion_qordered_gelu.py,sha256=6S_0bq0fJzfaGUPphPlamFZMCMb_RXRde_VELk0hOEM,4316
onnxruntime/transformers/fusion_qordered_layernorm.py,sha256=0bvr3NM-zV5mJ8HYkHq8MTfBpNqpjJt-F1Nj07CQhnQ,4834
onnxruntime/transformers/fusion_qordered_matmul.py,sha256=Qz8sZPPx5l6JB_taUBXio4aFLID8HHYFWy-bziesYRA,8349
onnxruntime/transformers/fusion_quickgelu.py,sha256=IC-urojZFn2LXt5Yjz0-iH0i7BBktozbuV49pmuXaCg,2795
onnxruntime/transformers/fusion_reshape.py,sha256=JryVrnVk622-MWk8EYNLlzz8AjA1SCHW_Tl9SrcnDmg,6230
onnxruntime/transformers/fusion_rotary_attention.py,sha256=JKsppeag3gjx4V0tmILmgAflOGm3MgerikM82zWgucc,66661
onnxruntime/transformers/fusion_shape.py,sha256=XSO5einjAKjzxPX_R8pqR9-FHNaG5hAvoizKZ1wAuZ8,3703
onnxruntime/transformers/fusion_simplified_layernorm.py,sha256=TvcVmfea-9SlMp_8ioyvLnW_2o2z_gBeeqBTxELlKYY,7311
onnxruntime/transformers/fusion_skip_group_norm.py,sha256=q39xIkhHwEVtg5izqM-O8f7zSVb9WXIo5W_UNmc8Wzk,10625
onnxruntime/transformers/fusion_skiplayernorm.py,sha256=GBusWhJyLHYOMnHMdlAbhAaRAy2cFdoKKyFPiTPWQ2g,8959
onnxruntime/transformers/fusion_transpose.py,sha256=XMCkQ7NO2pfSScvbZRtzSDXZ4fE3yH9khWoDjmUA0-Q,6867
onnxruntime/transformers/fusion_utils.py,sha256=YV7TP2gE00KoVV32Rmei1VS-O1MqJrs3LnmA7Tmhjf0,12466
onnxruntime/transformers/huggingface_models.py,sha256=3Y7w811zd811-wJ9yQFm0n8v7GxWyIlPVmZ-DsMcyPs,8963
onnxruntime/transformers/import_utils.py,sha256=sIvPo8Im0qI527kJFXNxqBK4_vi9XmpMPtKnfdePgK0,631
onnxruntime/transformers/io_binding_helper.py,sha256=foLAluBBaXiGTs-AW0Qm01bT-IotMPKGZzsL2de5zYo,16966
onnxruntime/transformers/large_model_exporter.py,sha256=CeiYBgSIjIVVfReKM9oFdatklwnS9xmuJzlRp6Gxzqk,14916
onnxruntime/transformers/machine_info.py,sha256=v49GtSLOLReOc7Mq9CB5qmqpX9x31hD2K-JsNu9CJBw,7001
onnxruntime/transformers/metrics.py,sha256=SS_vzT7afS5Qi80anuSmpv1eRXIVqRUyTjqIeAWamYA,5163
onnxruntime/transformers/models/bart/__init__.py,sha256=BwRvaUkfYSrGVdz2T6jCi-cH8gR9UD3q3GrtMtkJL8w,483
onnxruntime/transformers/models/bart/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/bart/__pycache__/export.cpython-311.pyc,,
onnxruntime/transformers/models/bart/export.py,sha256=YwcCfayoh23FNYQrbapwspTdL0qSRNJp28bAyrFOmE4,4187
onnxruntime/transformers/models/bert/__init__.py,sha256=BwRvaUkfYSrGVdz2T6jCi-cH8gR9UD3q3GrtMtkJL8w,483
onnxruntime/transformers/models/bert/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/bert/__pycache__/eval_squad.cpython-311.pyc,,
onnxruntime/transformers/models/bert/eval_squad.py,sha256=5mS1JvMZ6pWNnsvUTEBrd0NRiCbtDC9YfT-LDQcQyOo,12049
onnxruntime/transformers/models/gpt2/__init__.py,sha256=BwRvaUkfYSrGVdz2T6jCi-cH8gR9UD3q3GrtMtkJL8w,483
onnxruntime/transformers/models/gpt2/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/benchmark_gpt2.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_helper.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_parity.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/gpt2_tester.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/__pycache__/parity_check_helper.cpython-311.pyc,,
onnxruntime/transformers/models/gpt2/benchmark_gpt2.py,sha256=IVnI0ZacVbLQCcLDAkjbWahqPogpMAuXCxDc2UiWRKs,15517
onnxruntime/transformers/models/gpt2/convert_to_onnx.py,sha256=d-QHIymtE8oqGJ31ssIhTLJ_aOCnctyy3dsbqnBjdwQ,20144
onnxruntime/transformers/models/gpt2/gpt2_helper.py,sha256=riQ6Gs7Izgpt5aUqcuKkXRooz-XA2tGDteDLptyPpH8,40349
onnxruntime/transformers/models/gpt2/gpt2_parity.py,sha256=kyHo6N_WI99CYNppsny3kXnC_kwOMS2TwEsOnZUJ6aQ,17725
onnxruntime/transformers/models/gpt2/gpt2_tester.py,sha256=xYwCsT3X-pMvdlr77lOxU4SUr-if9A8xxoLVp0E6mZc,19519
onnxruntime/transformers/models/gpt2/parity_check_helper.py,sha256=FFBML7w0N8R1YD_Qsn-vAseSEdc06PGtc9ASyiJRbsA,5660
onnxruntime/transformers/models/llama/__init__.py,sha256=x0v-uxUz39CSawLG1RB-Jsdh9j5X2wlhS3KJuwvk3Pg,478
onnxruntime/transformers/models/llama/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark_all.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/benchmark_e2e.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/dist_settings.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_inputs.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_parity.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/llama_torch.cpython-311.pyc,,
onnxruntime/transformers/models/llama/__pycache__/quant_kv_dataloader.cpython-311.pyc,,
onnxruntime/transformers/models/llama/benchmark.py,sha256=kiYeOrrCKGkQc9JoqCFlmmBbWV4bqmAIQTILBtbFek0,26785
onnxruntime/transformers/models/llama/benchmark_all.py,sha256=qcPt3riX9Nx9l8lYgkxUI9zVVQqzVQM9_Hn3y2y98J8,15279
onnxruntime/transformers/models/llama/benchmark_e2e.py,sha256=G3CEMIPfZvzj8PE5LyfmDMyKoeb_dhcnAEZEIX_JAUo,24833
onnxruntime/transformers/models/llama/convert_to_onnx.py,sha256=MtzfTwleQLFm34lGiXPKCnWWsU3B3upZbew-bM0P9yI,40661
onnxruntime/transformers/models/llama/dist_settings.py,sha256=vhjsT8F32PbCVrEmPsWLEYb97cH38ajRggYNhuLS6co,1579
onnxruntime/transformers/models/llama/llama_inputs.py,sha256=XubGqXQBfCfNXA6Rcxig1RYLyCphf_NG_0wK9vIbrWI,19996
onnxruntime/transformers/models/llama/llama_parity.py,sha256=JF30zQNnlZTA7JP-Gx2z0GuT6CzozGuBgM8MjXZ0sPg,9926
onnxruntime/transformers/models/llama/llama_torch.py,sha256=cWP8x1GNB2McnPDSbAEJuhuHYuXZnPl8SrIccyq_bk4,1685
onnxruntime/transformers/models/llama/quant_kv_dataloader.py,sha256=sWIZ4UQpLcGdNkK7s6RgCnl8J5wcdMA17akQxoJ-Vkk,4851
onnxruntime/transformers/models/longformer/__init__.py,sha256=BwRvaUkfYSrGVdz2T6jCi-cH8gR9UD3q3GrtMtkJL8w,483
onnxruntime/transformers/models/longformer/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/benchmark_longformer.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/generate_test_data.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/__pycache__/longformer_helper.cpython-311.pyc,,
onnxruntime/transformers/models/longformer/benchmark_longformer.py,sha256=2hqrOJshioZ0uzcMgd1JZHZAz2G4QotnZoz23IvOm5U,29429
onnxruntime/transformers/models/longformer/convert_to_onnx.py,sha256=Ez0gfyY7y6EEnUDIzw6FzyI4kQYSNP7ojFjH98bTQ68,14806
onnxruntime/transformers/models/longformer/generate_test_data.py,sha256=0uRFApz5RyDUKr9wKlAErFTxv5XHMiXrAa_1HtJoehc,9617
onnxruntime/transformers/models/longformer/longformer_helper.py,sha256=Gy-bBf_cR09_GHzJOHwHRuTsX_lv8MH-07R8G8IvWZg,3103
onnxruntime/transformers/models/phi2/__init__.py,sha256=x0v-uxUz39CSawLG1RB-Jsdh9j5X2wlhS3KJuwvk3Pg,478
onnxruntime/transformers/models/phi2/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/phi2/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/phi2/__pycache__/inference_example.cpython-311.pyc,,
onnxruntime/transformers/models/phi2/convert_to_onnx.py,sha256=X88kloEYEt0uMWARoo075eer0fPq6rwxZmnd5ZNZyDo,19800
onnxruntime/transformers/models/phi2/inference_example.py,sha256=ZEVn0p_lezEGXQqimpHD-4-cJ1O_l9BmjTve9PRs7V8,17286
onnxruntime/transformers/models/sam2/__init__.py,sha256=BwRvaUkfYSrGVdz2T6jCi-cH8gR9UD3q3GrtMtkJL8w,483
onnxruntime/transformers/models/sam2/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/benchmark_sam2.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/image_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/image_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/mask_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/nvtx_helper.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/prompt_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/sam2_demo.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/sam2_image_onnx_predictor.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/__pycache__/sam2_utils.cpython-311.pyc,,
onnxruntime/transformers/models/sam2/benchmark_sam2.py,sha256=WEC2wYmZagerTpEjeuJhuqrS7Ay54t2A4dYUDTtU28E,21375
onnxruntime/transformers/models/sam2/convert_to_onnx.py,sha256=nytaPrrnU85fIpFZY1vvge3sMuZP0ZBbXO7iC4K_1bI,10086
onnxruntime/transformers/models/sam2/image_decoder.py,sha256=gBDVcMCVD64K1jrRsk0ySiMAhIvjCIQP4CpgoMCE3fc,10801
onnxruntime/transformers/models/sam2/image_encoder.py,sha256=1Pfu_uVR-RZJpZJ96k6vGyU6lPYIGRKPHy6-Tj6w8-c,7397
onnxruntime/transformers/models/sam2/mask_decoder.py,sha256=PtudONxol048euQ3kWxiFfi1S9gaOVHBYwFssTTrvag,8854
onnxruntime/transformers/models/sam2/nvtx_helper.py,sha256=MddufGWZpH9DIMJ0DeymuW7nPmI_zpGQxDSoYjS0Kh0,1279
onnxruntime/transformers/models/sam2/prompt_encoder.py,sha256=LNml1fH8Q_D_6OBdZhMyTIe5ONucT-AL-MjM1fQQJNI,8337
onnxruntime/transformers/models/sam2/sam2_demo.py,sha256=GVeYFNa20WWHK-ZDnODQSh-l4wGk9ezKi-y5xiS-giY,10485
onnxruntime/transformers/models/sam2/sam2_image_onnx_predictor.py,sha256=IAtMk6X27VAD_v0x-BP-3rjZiQgot2VI4Kcyo7ZgGOo,12495
onnxruntime/transformers/models/sam2/sam2_utils.py,sha256=Wd1LIeSIiQT2934WxgwGxplvuhaZPZxz5IyCc1W_ri8,5530
onnxruntime/transformers/models/stable_diffusion/__init__.py,sha256=BwRvaUkfYSrGVdz2T6jCi-cH8gR9UD3q3GrtMtkJL8w,483
onnxruntime/transformers/models/stable_diffusion/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark_controlnet.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img_xl.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_utils.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_models.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_schedulers.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_cuda.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_trt.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_tensorrt.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_torch.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/optimize_pipeline.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/ort_optimizer.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/pipeline_stable_diffusion.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/__pycache__/trt_utilities.cpython-311.pyc,,
onnxruntime/transformers/models/stable_diffusion/benchmark.py,sha256=lVn8JIcyVPi0J_e2OUKe5XJc2fjxJ1aGs27bUwO3k5M,46924
onnxruntime/transformers/models/stable_diffusion/benchmark_controlnet.py,sha256=iK6_niPQNaecTKNjWmVvbMgw7Dj17Zp9dsPsj0DHKxI,12827
onnxruntime/transformers/models/stable_diffusion/demo_txt2img.py,sha256=KoNQ3DQ5mkeJ-r3A07ANUgj9H3HpZdECY8E1ep_MwHY,3292
onnxruntime/transformers/models/stable_diffusion/demo_txt2img_xl.py,sha256=gmlImAC295RDP_02HUbYLbIHAkdI-YeTA65eyN1WgcE,9911
onnxruntime/transformers/models/stable_diffusion/demo_utils.py,sha256=q6q2Qr9XIzWngoCUDwN7gFv_RWdS_vH9CFE9fkpyYqw,28589
onnxruntime/transformers/models/stable_diffusion/diffusion_models.py,sha256=9mgCDGnYd23zSX0vvWmodsmxX5w5GNzBNJxft1FgVX4,50391
onnxruntime/transformers/models/stable_diffusion/diffusion_schedulers.py,sha256=TIQ4VPJ5i4poxzDCIRIR8KZUtY1XOlWWHlxeLH8E25c,48357
onnxruntime/transformers/models/stable_diffusion/engine_builder.py,sha256=O6l0bew7tG1x_-CHJaayBq9Ft7YMikCsxm2wC6vEr68,11684
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_cuda.py,sha256=jIUdwwsEnZvE1YuDOWLy_C3_0473raDj-zyqABnl2XU,15906
onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_trt.py,sha256=S9QE6YQoCKOkkYI7a40esmVTx4Ck7Y1SJP0WZX66jeM,11163
onnxruntime/transformers/models/stable_diffusion/engine_builder_tensorrt.py,sha256=GgdGGqZK1903-fYtQ3X8WbDcx28Clo6QCyp7z__WRwM,15604
onnxruntime/transformers/models/stable_diffusion/engine_builder_torch.py,sha256=NjWGu3PoFICMdFz1T23M3hAUzX-spimZWdTq9pIzE0k,4181
onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py,sha256=yykRAJERmqjdNnx9CDsNgP46MolV3a9teUV6RblghpM,12531
onnxruntime/transformers/models/stable_diffusion/ort_optimizer.py,sha256=wVCceE1-YT6sO1BOv_JShu-cBF1_qHjy9K63Uynn3RE,5700
onnxruntime/transformers/models/stable_diffusion/pipeline_stable_diffusion.py,sha256=Ag9VeMiqVcsfnrHlfq_KqfxiExLRQcyh17Evo8fAhYA,33164
onnxruntime/transformers/models/stable_diffusion/trt_utilities.py,sha256=8CHgIIwBriqyR0gULVPFI1uF1rbAH49_iMPUdQvOnAI,420
onnxruntime/transformers/models/t5/__init__.py,sha256=BwRvaUkfYSrGVdz2T6jCi-cH8gR9UD3q3GrtMtkJL8w,483
onnxruntime/transformers/models/t5/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/past_helper.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_encoder_decoder_init.cpython-311.pyc,,
onnxruntime/transformers/models/t5/__pycache__/t5_helper.cpython-311.pyc,,
onnxruntime/transformers/models/t5/convert_to_onnx.py,sha256=O1q3RvTK0ZgS-SB5SsMX2VsyoGdIYIublhEvR75ZzHg,8732
onnxruntime/transformers/models/t5/past_helper.py,sha256=iDS9qzo6nnBkrwRjmiQN91MOk9D3WcuZqPOXunQ4KGA,6837
onnxruntime/transformers/models/t5/t5_decoder.py,sha256=ZCN-Rn1A63q6udBz2m1WK7uiQDYdK94TBSu-aPNB3Pg,16824
onnxruntime/transformers/models/t5/t5_encoder.py,sha256=EOqxIrTQfQQsn6AdkupOOsT1sA6h5YrRM_X9YGVYR3E,6124
onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py,sha256=5EvYjQfOtdVJQ_6AnsvH7nsJ_pTYzRWvWFQCg-UEesI,11974
onnxruntime/transformers/models/t5/t5_helper.py,sha256=wAEanwNmakNLllKc-bEpsqiXWC69tsy4-Bytgqkk-DQ,10760
onnxruntime/transformers/models/whisper/__init__.py,sha256=x0v-uxUz39CSawLG1RB-Jsdh9j5X2wlhS3KJuwvk3Pg,478
onnxruntime/transformers/models/whisper/__pycache__/__init__.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/benchmark.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/benchmark_all.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/convert_to_onnx.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_chain.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_decoder.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder_decoder_init.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_helper.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/__pycache__/whisper_openai_helper.cpython-311.pyc,,
onnxruntime/transformers/models/whisper/benchmark.py,sha256=bOngsnLi-JK8VGsAQQIFKNk8wPLMF8-chbi9jR0-9Xo,22766
onnxruntime/transformers/models/whisper/benchmark_all.py,sha256=AIKiexu2vT2zmR29EMrb-0w_kXrIFVGmEenaJr6VSPE,18849
onnxruntime/transformers/models/whisper/convert_to_onnx.py,sha256=kUIiZ7fL1GgQJPnOQTgc_IwjySYH7sTbALfNV8S0DnY,17868
onnxruntime/transformers/models/whisper/whisper_chain.py,sha256=ACB2RFLVx8raJ4rArNaQUZtyGVTJDmDALySqdRB0KDc,14732
onnxruntime/transformers/models/whisper/whisper_decoder.py,sha256=XEw9ML5vaSzTYzQ0bxP5Vt3DjG2tSaegzzGpvjbLCAU,15619
onnxruntime/transformers/models/whisper/whisper_encoder.py,sha256=fwBGvUV1c3CE18U5_2aRqMMaB0W5-uyGG2Uzx3l0Bac,5576
onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py,sha256=ZCvhpQF2OIoFa5S8RKG05SM2oJ-J9HpYP2syvGKcIjE,12417
onnxruntime/transformers/models/whisper/whisper_helper.py,sha256=vhE5ZckyzxIVh_s5aaHy1-OCB0VHiNzfDJq7q5mV88o,22963
onnxruntime/transformers/models/whisper/whisper_openai_helper.py,sha256=Z4onA293kt7kgQCZqd1DPcCmWTGkrcgxrdXOccoAjSA,3188
onnxruntime/transformers/onnx_exporter.py,sha256=6mviIJewjYV7pJDC-Kb-4Bnh2Z9VUXnYi6-0cb51vEA,24459
onnxruntime/transformers/onnx_model.py,sha256=sE_jlJS5UcbbhuCXFlto0pHTUjMd5e72wQqx59BnvOE,66001
onnxruntime/transformers/onnx_model_bart.py,sha256=O2v86Embb3zg1WD1UTVxQ61OUGn0gh0Ib7UUnSDQNYY,5437
onnxruntime/transformers/onnx_model_bert.py,sha256=uD0D7vn9uCYA0dQdOdg-7lt8BZuGmsvuip4oc82l0ZM,19676
onnxruntime/transformers/onnx_model_bert_keras.py,sha256=M3O7yCYscu8UYpeC-fC75my8pP8v_qmJF9L_Vb6SlVk,18465
onnxruntime/transformers/onnx_model_bert_tf.py,sha256=QOHA1zq69X7Q3Pv4ygiSBzE4Ptc1K9h9RQewz9rncNY,24844
onnxruntime/transformers/onnx_model_clip.py,sha256=MpTBe1nPKtkIF67Hyivl9x2V6_0IeAtYXGZrUTAzCMY,1304
onnxruntime/transformers/onnx_model_conformer.py,sha256=UAKV7Ac_GaK3oaAp9KT2qt_k4hYHziS7pmPl8ALG3qI,1411
onnxruntime/transformers/onnx_model_gpt2.py,sha256=XDGHsn69dnZwvP3kjG3qUB4QKDULWGeBXg_YeJ2ssac,3812
onnxruntime/transformers/onnx_model_phi.py,sha256=7MQINHBb53z1fxVy24ZCZ1xLOEMRp4Zaxy0TJ6StPv0,35447
onnxruntime/transformers/onnx_model_sam2.py,sha256=pvwvMa-SCg4rnLNnNVlCsNvqrf11I4mLJqzA_X3JP7U,4847
onnxruntime/transformers/onnx_model_t5.py,sha256=wSyCgmjH2EN3IZPT2aktz8tvIG9Ct7E1U0Tlk9Tsw58,28141
onnxruntime/transformers/onnx_model_tnlr.py,sha256=_pVfybbaWGUOvt_-PrvqMPzzsZzTS7xJJmCzfywmfj0,8210
onnxruntime/transformers/onnx_model_unet.py,sha256=QtLUfjrAetSn38gAseLmtj19y-e9g93eG_CoKtMtYgs,9258
onnxruntime/transformers/onnx_model_vae.py,sha256=lROIHs_LXB7HMILG-V_-jV7ndf4D1BpkZP0Pf5OHRW0,1502
onnxruntime/transformers/onnx_utils.py,sha256=N1qnQ_iChwBs7LWv07jK6ZPAneyhnBeW7CCBzKqyxxE,2106
onnxruntime/transformers/optimizer.py,sha256=gX2HTN4766Gu-bq2oYIiGvZw3HK6HtAwgbfX-j2rgj8,24704
onnxruntime/transformers/profiler.py,sha256=75580jQoMcYGRi5RlvLxlSIesgwtdgV-R3TN8U5FSfI,24166
onnxruntime/transformers/quantize_helper.py,sha256=8P2wuOO5P-_h7CtWKRNQEkio-tDy8ndYdiqCPC-CWOw,2809
onnxruntime/transformers/shape_infer_helper.py,sha256=Yvs8KXmy9qAbjRKY9lfk_1VKuoSS1Beb6iISZs9eGns,4469
onnxruntime/transformers/shape_optimizer.py,sha256=un2W3DAZntPgdlEicuBEsFB5s-Hyv7YxKfv3p3_xiQE,15105
onnxruntime/transformers/torch_onnx_export_helper.py,sha256=Xrctke29WmIAek-_rm4A4LugkDtFaCrL2P8eK72bW4g,2501
